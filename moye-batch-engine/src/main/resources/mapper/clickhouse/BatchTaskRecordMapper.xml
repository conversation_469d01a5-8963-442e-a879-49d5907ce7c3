<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.moye.batch.engine.dao.clickhouse.BatchTaskRecordMapper">
  <!-- 结果集映射 -->
  <resultMap id="BaseResultMap" type="com.trs.moye.batch.engine.entity.BatchTaskRecord">
    <result column="execute_id" jdbcType="VARCHAR" property="executeId" />
    <result column="application_id" jdbcType="VARCHAR" property="applicationId" />
    <result column="trigger_mode" property="triggerMode" />
    <result column="task_id" jdbcType="INTEGER" property="taskId" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="is_error" jdbcType="TINYINT" property="isError" />
    <result column="error_msg_read_flag" jdbcType="INTEGER" property="errorMsgReadFlag" />
    <result column="storage_success_count" jdbcType="BIGINT" property="storageSuccessCount"/>
    <result column="layer" jdbcType="VARCHAR" property="layer"/>
    <result column="status" jdbcType="VARCHAR" property="status"/>
    <result column="retry_xxl_job_id" jdbcType="INTEGER" property="retryXxlJobId"/>
  </resultMap>
  <!-- 属性列表 -->
  <sql id="Base_Column_List">
    <trim suffixOverrides=",">
      execute_id,
      application_id,
      trigger_mode,
      task_id,
      task_name,
      start_time,
      end_time,
      is_error,
      error_msg_read_flag,
      storage_success_count,
      layer,
      write_count_info,
      `status`,
      retry_xxl_job_id
    </trim>
  </sql>
  <!-- 普通插入属性列表，注意与Base_Column_List属性列表一一对应 -->
  <sql id="Insert_Property_List">
    <trim suffixOverrides=",">
      #{data.executeId,jdbcType=VARCHAR},
      #{data.applicationId,jdbcType=VARCHAR},
      #{data.triggerMode,jdbcType=VARCHAR},
      #{data.taskId,jdbcType=INTEGER},
      #{data.taskName,jdbcType=VARCHAR},
      #{data.startTime,jdbcType=TIMESTAMP},
      #{data.endTime,jdbcType=TIMESTAMP},
      #{data.isError,jdbcType=TINYINT},
      #{data.errorMsgReadFlag,jdbcType=INTEGER},
      #{data.storageSuccessCount,jdbcType=INTEGER},
      #{data.layer,jdbcType=VARCHAR},
      #{data.writeCountInfo,jdbcType=VARCHAR,typeHandler=com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler},
      #{data.status,jdbcType=VARCHAR},
      #{data.retryXxlJobId,jdbcType=INTEGER}
    </trim>
  </sql>
  
  <!-- 动态修改属性列表（非主键属性） -->
  <sql id="Update_Selective_Property_List">
    <trim suffixOverrides=",">
      <if test="data.applicationId != null">
        application_id = #{data.applicationId,jdbcType=VARCHAR},
      </if>
      <if test="data.triggerMode != null">
        trigger_mode = #{data.triggerMode,jdbcType=VARCHAR},
      </if>
      <if test="data.taskId != null">
        task_id = #{data.taskId,jdbcType=INTEGER},
      </if>
      <if test="data.taskName != null">
        task_name = #{data.taskName,jdbcType=VARCHAR},
      </if>
      <if test="data.endTime != null">
        end_time = #{data.endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="data.isError != null">
        is_error = #{data.isError,jdbcType=TINYINT},
      </if>
      <if test="data.errorMsgReadFlag != null">
        error_msg_read_flag = #{data.errorMsgReadFlag,jdbcType=INTEGER},
      </if>
      <if test="data.storageSuccessCount!= null">
        storage_success_count = #{data.storageSuccessCount,jdbcType=INTEGER},
      </if>
      <if test="data.layer!= null">
        layer = #{data.layer,jdbcType=VARCHAR},
      </if>
      <if test="data.writeCountInfo != null">
        write_count_info = #{data.writeCountInfo,jdbcType=VARCHAR,typeHandler=com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler},
      </if>
      <if test="data.status != null">
        `status` = #{data.status,jdbcType=VARCHAR},
      </if>
      <if test="data.retryXxlJobId != null">
        retry_xxl_job_id = #{data.retryXxlJobId,jdbcType=INTEGER},
      </if>
    </trim>
  </sql>
  
  <insert id="insert">
    insert into batch_task_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <include refid="Base_Column_List" />
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <include refid="Insert_Property_List" />
    </trim>
  </insert>
  <insert id="insertBatch">
    insert into batch_task_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <include refid="Base_Column_List" />
    </trim>
    values
    <foreach collection="dataCollection" item="data" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        <include refid="Insert_Property_List" />
      </trim>
    </foreach>
  </insert>
  <update id="updateSelective">
    update batch_task_record
    <set>
      <include refid="Update_Selective_Property_List"/>
    </set>
    <where>
      execute_id = #{data.executeId,jdbcType=VARCHAR}
    </where>
  </update>
  <delete id="deleteByExecuteId">
    delete from batch_task_record
    <where>
      and execute_id = #{executeId,jdbcType=VARCHAR}
    </where>
  </delete>
  <delete id="deleteByApplicationId">
    delete from batch_task_record
    <where>
      and application_id = #{applicationId,jdbcType=VARCHAR}
    </where>
  </delete>
  <delete id="deleteByTaskId">
    delete from batch_task_record
    <where>
      and task_id = #{taskId,jdbcType=INTEGER}
    </where>
  </delete>
  <delete id="deleteByExecuteIdCollection">
    delete from batch_task_record
    <where>
      <choose>
        <when test="executeIdCollection == null or executeIdCollection.size == 0">
          and false
        </when>
        <otherwise>
          and execute_id in 
          <foreach close=")" collection="executeIdCollection" item="executeId" open="(" separator=",">
            #{executeId,jdbcType=VARCHAR}
          </foreach>
        </otherwise>
      </choose>
    </where>
  </delete>
  <delete id="deleteByApplicationIdCollection">
    delete from batch_task_record
    <where>
      <choose>
        <when test="applicationIdCollection == null or applicationIdCollection.size == 0">
          and false
        </when>
        <otherwise>
          and application_id in 
          <foreach close=")" collection="applicationIdCollection" item="applicationId" open="(" separator=",">
            #{applicationId,jdbcType=VARCHAR}
          </foreach>
        </otherwise>
      </choose>
    </where>
  </delete>
  <delete id="deleteByTaskIdCollection">
    delete from batch_task_record
    <where>
      <choose>
        <when test="taskIdCollection == null or taskIdCollection.size == 0">
          and false
        </when>
        <otherwise>
          and task_id in 
          <foreach close=")" collection="taskIdCollection" item="taskId" open="(" separator=",">
            #{taskId,jdbcType=INTEGER}
          </foreach>
        </otherwise>
      </choose>
    </where>
  </delete>
  <select id="getByExecuteId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from batch_task_record
    <where>
      and execute_id = #{executeId,jdbcType=VARCHAR}
    </where>
  </select>

  <select id="getLastBatchTaskRecord" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from batch_task_record
    <where>
      and task_id = #{taskId,jdbcType=VARCHAR}
    </where>
    order by start_time desc
    limit 1 offset 1;
  </select>

  <select id="getLastSuccessBatchTaskRecord" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from batch_task_record
    <where>
      and task_id = #{taskId,jdbcType=VARCHAR}
      and is_error = 0
    </where>
    order by start_time desc
    limit 1 offset 1;
  </select>

  <select id="getByApplicationId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from batch_task_record
    <where>
      and application_id = #{applicationId,jdbcType=VARCHAR}
    </where>
  </select>
  <select id="listByTaskId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from batch_task_record
    <where>
      and task_id = #{taskId,jdbcType=INTEGER}
    </where>
  </select>
  <select id="listByExecuteIdCollection" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from batch_task_record
    <where>
      <choose>
        <when test="executeIdCollection == null or executeIdCollection.size == 0">
          and false
        </when>
        <otherwise>
          and execute_id in 
          <foreach close=")" collection="executeIdCollection" item="executeId" open="(" separator=",">
            #{executeId,jdbcType=VARCHAR}
          </foreach>
        </otherwise>
      </choose>
    </where>
  </select>
  <select id="listByApplicationIdCollection" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from batch_task_record
    <where>
      <choose>
        <when test="applicationIdCollection == null or applicationIdCollection.size == 0">
          and false
        </when>
        <otherwise>
          and application_id in 
          <foreach close=")" collection="applicationIdCollection" item="applicationId" open="(" separator=",">
            #{applicationId,jdbcType=VARCHAR}
          </foreach>
        </otherwise>
      </choose>
    </where>
  </select>
  <select id="listByTaskIdCollection" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from batch_task_record
    <where>
      <choose>
        <when test="taskIdCollection == null or taskIdCollection.size == 0">
          and false
        </when>
        <otherwise>
          and task_id in
          <foreach close=")" collection="taskIdCollection" item="taskId" open="(" separator=",">
            #{taskId,jdbcType=INTEGER}
          </foreach>
        </otherwise>
      </choose>
    </where>
  </select>

  <update id="updateApplicationId">
    update batch_task_record
    <set>
      <trim suffixOverrides=",">
        <if test="applicationId != null">
          application_id = #{applicationId,jdbcType=VARCHAR},
        </if>
      </trim>
    </set>
    <where>
      execute_id = #{executeId,jdbcType=VARCHAR}
    </where>
  </update>

  <update id="updateWhenLostTask">
    update batch_task_record
    <set>
      <trim suffixOverrides=",">
        <if test="endTime != null">
          end_time = #{endTime,jdbcType=TIMESTAMP},
        </if>
        <if test="killed != null">
          `status` = #{killed,jdbcType=VARCHAR},
        </if>
      </trim>
    </set>
    <where>
      execute_id in
      <foreach item="id" collection="executeIdList" open="(" separator="," close=")">
        #{id}
      </foreach>
    </where>
  </update>

  <select id="getPreviousBatchTaskRecordByTriggerMode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from batch_task_record
    <where>
      and task_id = #{taskId,jdbcType=INTEGER}
      and trigger_mode = #{triggerMode,jdbcType=VARCHAR}
      <if test="executeId != null">
        and start_time &lt; (select start_time from batch_task_record where execute_id = #{executeId,jdbcType=VARCHAR})
      </if>
    </where>
    order by start_time desc
    limit 1;
  </select>
</mapper>