package com.trs.moye.batch.engine.service;

import com.trs.ai.moye.minio.starter.properties.MinioProperties;
import com.trs.moye.base.common.response.ResponseMessage;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.batch.engine.entity.BatchTaskRecord;
import com.trs.moye.batch.engine.entity.BatchTaskRecord.TaskWriteCount;
import com.trs.moye.batch.engine.entity.BatchTaskTracer;
import com.trs.moye.batch.engine.entity.vo.BatchTaskVO;
import com.trs.moye.batch.engine.entity.vo.ExecuteResultMap;
import com.trs.moye.batch.engine.entity.vo.ExecuteResultRequest;
import com.trs.moye.batch.engine.enums.CodeTypeEnum;
import com.trs.moye.batch.engine.feign.MoyeService;
import com.trs.moye.batch.engine.hive.SqlTaskContext;
import com.trs.moye.batch.engine.hive.SqlTaskExecutor;
import com.trs.moye.batch.engine.spark.SparkApplicationCallbackImpl;
import com.trs.moye.batch.engine.spark.SparkApplicationContext;
import com.trs.moye.batch.engine.spark.SparkApplicationExecutor;
import com.trs.moye.batch.engine.utils.LogParseUtil;
import com.trs.moye.batch.engine.websocket.SessionManager;
import feign.FeignException;
import io.minio.GetObjectArgs;
import io.minio.MinioClient;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 负责 moye批处理任务 的 执行
 */
@Slf4j
@Service
public class BatchTaskService {

    @Resource
    SparkApplicationExecutor sparkApplicationExecutor;
    @Resource
    SqlTaskExecutor sqlTaskExecutor;
    @Resource
    TaskApiService taskApiService;
    @Resource
    private MoyeService moyeService;
    @Resource
    private MinioProperties minioProperties;
    @Resource
    private MinioClient minioClient;
    @Resource
    private DataStorageService dataStorageService;
    @Resource
    private BatchTaskRedisService batchTaskRedisService;


    /*
    代码模式
     */

    /**
     * 代码模式
     *
     * @param tasks 任务
     * @param needRealTimeLog  触发模式
     * @return 执行结果
     */
    public ResponseMessage executeCodeTasks(List<BatchTaskVO> tasks, boolean needRealTimeLog) {
        // 代码模式子任务统一都用一致的执行id
        String executeId = tasks.get(0).getExecuteId();

        // 查询存储点数据量
        List<DataStorage> dataStorages = dataStorageService.getDataStoragesWithDataCount(
            Integer.parseInt(tasks.get(0).getTaskId()));

        // 记录任务执行异常，以阻止后续任务触发
        Throwable exception = null;

        for (BatchTaskVO task : tasks) {

            // 代码模式子任务统一都用一致的执行id
            task.setExecuteId(executeId);

            // 立即执行的任务需要实时日志，即反馈websocket日志
            whenCodeTaskStart(needRealTimeLog, task);

            // 执行任务
            final LocalDateTime beginTime = LocalDateTime.now();
            Exception subTaskException = null;
            try {
                executeCodeTask(task, needRealTimeLog);
            } catch (Exception e) {
                subTaskException = e;
                exception = e;
                log.error("执行任务异常 [taskId:{}, taskName:{}, subtaskName:{}]", task.getTaskId(),
                    task.getTaskName(), task.getSubTaskName(), e);
            }

            // 任务结束, ws和ck日志
            whenCodeTaskEnd(needRealTimeLog, task, beginTime, subTaskException);

            // 发生异常，停止后续任务
            if (Objects.nonNull(subTaskException)) {
                taskApiService.sendNotice(task, subTaskException.getMessage());
                if (!Boolean.TRUE.equals(task.getContinueOnException())) {
                    break;
                }
            }
        }

        // 所有子任务结束
        whenCodeTasksEnd(executeId, tasks, LocalDateTime.now(), exception, dataStorages);

        if (Objects.nonNull(exception)) {
            return ResponseMessage.error(exception.getMessage());
        } else {
            return ResponseMessage.ok("批处理任务执行成功");
        }
    }

    private void whenCodeTaskStart(boolean needRealTimeLog, BatchTaskVO task) {
        // 立即执行的任务: 发送websocket日志 表明任务启动
        if (needRealTimeLog) {
            SessionManager.getInstance()
                .sendBeginTaskMessage(task.getTaskId(), task.getUserId(),
                    Optional.ofNullable(task.getSubTaskName()).orElse(task.getTaskName()));
        }
    }

    private void whenCodeTaskEnd(boolean needRealTimeLog, BatchTaskVO task, LocalDateTime beginTime,
        Throwable exception) {
        // 打印ws任务结束日志
        if (needRealTimeLog) {
            SessionManager.getInstance()
                .sendEndTaskMessage(task.getTaskId(), task.getUserId(),
                    Optional.ofNullable(task.getSubTaskName()).orElse(task.getTaskName()));
        }

        // 记录子任务执行结果
        BatchTaskTracer tracer = BatchTaskTracer.task(task.getExecuteId(), task.getSubTaskName(), exception,
            beginTime, LocalDateTime.now());
        BatchTaskMonitor.insertTracer(tracer);
    }

    private void whenCodeTasksEnd(String executeId, List<BatchTaskVO> tasks, LocalDateTime endTime, Throwable exception,
        List<DataStorage> dataStorages) {
        // 所有子任务结束，回调逆向建模
        if (!tasks.isEmpty()) {
            try {
                moyeService.reverseModelingAuto(Integer.parseInt(tasks.get(0).getTaskId()));
            } catch (FeignException e) {
                log.warn("代码模式结束后触发逆向建模发生异常, {}", e.getMessage());
            }
        }

        // 查询存储点数据增长量，取存储点中此项最大值
        Long count = 0L;
        List<TaskWriteCount> taskWriteCounts = new ArrayList<>();
        if (Objects.nonNull(dataStorages)) {
            count = dataStorageService.getMaxDataGrowth(dataStorages);
            taskWriteCounts = dataStorageService.getStorageCountByStorageId(dataStorages);
            log.info("批处理任务执行结束，taskWriteCounts: {}, 存储点信息: {}",
                JsonUtils.toJsonString(taskWriteCounts), JsonUtils.toJsonString(dataStorages));
        }

        // ck record 结束日志
        BatchTaskMonitor.insertOrUpdateRecord(
            BatchTaskRecord.endTask(executeId, exception, endTime, count,
                taskWriteCounts.toArray(new TaskWriteCount[0])));

        // ck tracer 结束阶段日志
        BatchTaskMonitor.insertTracer(BatchTaskTracer.endTask(executeId, exception, endTime));
    }

    /**
     * 代码模式 python和sql分流
     *
     * @param task     任务
     * @param sendLog  是否发送日志
     */
    private void executeCodeTask(BatchTaskVO task, boolean sendLog) {
        if (CodeTypeEnum.SQL.equals(task.getCodeType())) {
            SqlTaskContext sqlTaskContext = task.createSqlTaskContext();
            sqlTaskExecutor.executeSql(sqlTaskContext, sendLog);
        } else {
            SparkApplicationContext context = task.createSparkApplicationContext();
            sparkApplicationExecutor.execute(context, sendLog,
                new SparkApplicationCallbackImpl(sendLog, task.getTaskId(), task.getExecuteId(), task.getUserId(),
                    task.getPrincipal(), task.getKeytabPath()));
        }
    }


    /**
     * 执行java任务
     *
     * @param task        任务信息
     * @param needRealTimeLog 立即执行的任务需要实时日志，即反馈websocket日志
     * @return 执行结果
     */
    public ResponseMessage executeDagTask(BatchTaskVO task, boolean needRealTimeLog) {

        // 查询存储点数据量，用于数据处理量统计
        List<DataStorage> dataStorages = dataStorageService.getDataStoragesWithDataCount(
            Integer.parseInt(task.getTaskId()));

        // 任务开始
        whenDagTaskStart(needRealTimeLog, task);

        // 执行任务
        Throwable exception = null;
        try {
            SparkApplicationContext context = task.createJavaApplicationContext();
            sparkApplicationExecutor.executeJava(context, needRealTimeLog,
                new SparkApplicationCallbackImpl(needRealTimeLog, task.getTaskId(), task.getExecuteId(),
                    task.getUserId(), task.getPrincipal(), task.getKeytabPath()));
        } catch (Exception e) {
            exception = e;
            log.error("java任务执行异常 [taskId:{}, taskName:{}]", task.getTaskId(), task.getTaskName(), e);
            //异常时调用发送通知方法
            taskApiService.sendNotice(task, e.getMessage());
        }

        // 任务结束
        whenDagTaskEnd(needRealTimeLog, task, LocalDateTime.now(), exception, dataStorages);

        if (Objects.nonNull(exception)) {
            return ResponseMessage.error(exception.getMessage());
        } else {
            return ResponseMessage.ok("批处理任务执行成功");
        }
    }

    /**
     * 任务开始时 记录日志以及ws日志
     *
     * @param needRealTimeLog 是否需要ws日志
     * @param task            任务
     */
    private void whenDagTaskStart(boolean needRealTimeLog, BatchTaskVO task) {
        // 立即执行的任务: 发送websocket日志 表明任务启动
        if (needRealTimeLog) {
            SessionManager.getInstance()
                .sendBeginTaskMessage(task.getTaskId(), task.getUserId(),
                    Optional.ofNullable(task.getSubTaskName()).orElse(task.getTaskName()));
        }

        // 更新算子信息到redis上
        batchTaskRedisService.updateOperatorToRedis(Integer.parseInt(task.getTaskId()), task.getTaskName(), task.getBeginTime(), task.getEndTime());
    }

    /**
     * 任务结束时 记录日志以及ws日志
     *
     * @param needRealTimeLog 是否需要ws日志
     * @param task            任务
     * @param endTime         结束时间
     * @param exception       任务中发生的错误
     * @param dataStorages    存储点
     */
    private void whenDagTaskEnd(boolean needRealTimeLog, BatchTaskVO task,
        LocalDateTime endTime, Throwable exception, List<DataStorage> dataStorages) {

        // 打印ws任务结束日志
        if (needRealTimeLog) {
            SessionManager.getInstance()
                .sendEndTaskMessage(task.getTaskId(), task.getUserId(),
                    Optional.ofNullable(task.getSubTaskName()).orElse(task.getTaskName()));
        }

        // 查询存储点数据增长量，取存储点中此项最大值
        Long count = 0L;
        List<TaskWriteCount> taskWriteCounts = new ArrayList<>();
        if (Objects.nonNull(dataStorages)) {
            count = dataStorageService.getMaxDataGrowth(dataStorages);
            taskWriteCounts = dataStorageService.getStorageCountByStorageId(dataStorages);
            log.info("批处理任务执行结束，taskWriteCounts: {}, 存储点信息: {}",
                JsonUtils.toJsonString(taskWriteCounts), JsonUtils.toJsonString(dataStorages));
        }
        // 记录到ck record上 任务结束日志
        BatchTaskMonitor.insertOrUpdateRecord(BatchTaskRecord.endTask(task.getExecuteId(), exception, endTime, count,
            taskWriteCounts.toArray(new TaskWriteCount[0])));

        // 记录到ck tracer上 任务的算子执行日志和结束阶段日志
        BatchTaskMonitor.insertTracer(BatchTaskTracer.endTask(task.getExecuteId(), exception, endTime));
    }


    /*
    TODO: 以下功能疑似可以放到moye上
     */

    /**
     * 获取执行结果
     *
     * @param executeResultRequest 参数
     * @return 执行结果
     */
    public List<ExecuteResultMap> executeResult(ExecuteResultRequest executeResultRequest) {
        Integer dataModelId = executeResultRequest.getDataModelId();
        List<ExecuteResultMap> resultMaps = new ArrayList<>();
        executeResultRequest.getExecuteIds()
            .forEach(executeId -> getLogs(executeResultRequest, executeId, dataModelId, resultMaps));
        log.info("成功获取数据");
        return resultMaps;
    }

    private void getLogs(ExecuteResultRequest executeResultRequest, String executeId, Integer dataModelId,
        List<ExecuteResultMap> resultMaps) {
        String logs = minioProperties.getBucket().getLogs();
        log.info("获取到的日志目录: {}", logs);
        // 获取对象
        log.info("获取到的建模具体日志路径:{}", dataModelId + "/" + executeId + ".log");
        try {
            InputStream stream = minioClient.getObject(
                GetObjectArgs.builder()
                    .bucket(logs)
                    .object(dataModelId + "/" + executeId + ".log")
                    .build()
            );
            List<ExecuteResultMap> executeResultMaps = LogParseUtil.parseHiveLog(stream, executeResultRequest);
            executeResultMaps.forEach(map -> map.setExecuteId(executeId));
            resultMaps.addAll(executeResultMaps);
            log.info("获取到的执行日志:{}", executeResultMaps);
        } catch (Exception ex) {
            log.error("获取日志失败! 原因: {}", ex.getMessage());
        }
    }
}
