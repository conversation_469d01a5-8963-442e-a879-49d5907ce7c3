package com.trs.moye.batch.engine.entity.vo;

import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.batch.engine.constants.SparkConstants.TaskParameter;
import com.trs.moye.batch.engine.enums.CodeTypeEnum;
import com.trs.moye.batch.engine.hive.SqlTaskContext;
import com.trs.moye.batch.engine.spark.SparkApplicationContext;
import com.trs.moye.batch.engine.utils.BatchIdUtil;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;


/**
 * 批处理任务 数据传输类
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class BatchTaskVO {

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 分层
     */
    private ModelLayer layer;

    /**
     * 用于 spark 应用名称
     */
    private String taskName;

    /**
     * 如果是代码模式 代码
     */
    private String code;

    /**
     * kerberos认证 用户名
     */
    private String principal;

    /**
     * kerberos认证 keytab文件
     */
    private String keytabPath;

    /**
     * 高级参数
     */
    private Map<String, String> configParameters = new HashMap<>();

    /**
     * 自定义 代码可替换参数
     */
    private Map<String, String> customCodeParameters = new HashMap<>();

    /**
     * 子任务名称
     */
    private String subTaskName;
    /**
     * 代码类型
     */
    private CodeTypeEnum codeType;
    /**
     * 启动任务用户id（立即执行才有）
     */
    private Integer userId;
    /**
     * 执行id
     */
    private String executeId = BatchIdUtil.buildExecuteId();
    /**
     * 遇到异常, 是否继续子任务
     * true: 继续执行后续子任务
     * false: 停止执行后续子任务
     */
    private Boolean continueOnException = false;

    private LocalDateTime beginTime;

    private LocalDateTime endTime;

    /**
     * 根据批处理任务传输类获取spark应用上下文
     *
     * @return {@link SparkApplicationContext} spark任务上下文
     */
    public SparkApplicationContext createSparkApplicationContext() {
        // kerberos认证相关参数放进spark任务参数
        Map<String, String> configParametersWithKerberos = getConfigParametersWithKerberos();
        SparkApplicationContext sparkApplicationContext = new SparkApplicationContext(executeId, this.taskId,
            createFullTaskName(),
            this.userId,
            configParametersWithKerberos, this.customCodeParameters);
        sparkApplicationContext.setCode(code);
        return sparkApplicationContext;
    }

    private String createFullTaskName() {
        return StringUtils.isNotBlank(this.subTaskName) ? this.taskName + '_' + this.subTaskName : this.taskName;
    }

    /**
     * 根据批处理任务传输类获取spark应用上下文
     *
     * @return {@link SparkApplicationContext} spark任务上下文
     */
    public SparkApplicationContext createJavaApplicationContext() {
        Map<String, String> configParametersWithKerberos = getConfigParametersWithKerberos();
        return new SparkApplicationContext(executeId, this.taskId, this.taskName,
            userId, configParametersWithKerberos, this.customCodeParameters);
    }

    private Map<String, String> getConfigParametersWithKerberos() {
        Map<String, String> configParametersWithKerberos = new HashMap<>();
        if (this.configParameters != null) {
            configParametersWithKerberos.putAll(configParameters);
        }
        // kerberos认证相关参数放进spark任务参数
        if (this.principal != null) {
            configParametersWithKerberos.put(TaskParameter.KERBEROS_PRINCIPAL, this.principal);
        }
        if (this.keytabPath != null) {
            configParametersWithKerberos.put(TaskParameter.KERBEROS_KEYTAB, this.keytabPath);
        }
        return configParametersWithKerberos;
    }

    /**
     * 创建sql任务上下文
     *
     * @return {@link SqlTaskContext}
     */
    public SqlTaskContext createSqlTaskContext() {
        SqlTaskContext sqlTaskContext = new SqlTaskContext();
        sqlTaskContext.setCode(code);
        sqlTaskContext.setUserId(userId);
        sqlTaskContext.setTaskId(taskId);
        sqlTaskContext.setTaskName(createFullTaskName());
        sqlTaskContext.setExecutionId(executeId);
        sqlTaskContext.setTaskType("sql");
        return sqlTaskContext;
    }

}
