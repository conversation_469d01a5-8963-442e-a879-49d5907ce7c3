package com.trs.ai.moye.data.model.service;

import com.trs.ai.moye.data.model.request.FileImportDataRequest;
import com.trs.ai.moye.data.model.request.FileModelingRequest;
import com.trs.ai.moye.data.model.response.DataModelFileUploadResponse;
import com.trs.ai.moye.data.model.response.FileImportDataResponse;
import com.trs.ai.moye.data.model.response.FileModelingResponse;
import com.trs.moye.base.data.model.field.mapping.FieldMappingResponse;
import java.io.IOException;
import java.util.List;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件建模服务接口
 */
public interface FileModelingService {

    /**
     * 文件上传接口 用于文件建模
     *
     * @param file 上传的文件
     * @return {@link DataModelFileUploadResponse}
     */
    DataModelFileUploadResponse uploadFile(MultipartFile file) throws IOException;

    /**
     * 文件建模
     *
     * @param request 建模请求
     * @return {@link List}<{@link FileModelingResponse}>
     */
    FileModelingResponse fileModelingBatch(FileModelingRequest request);

    /**
     * 获取字段映射信息
     *
     * @param fileName  文件名称
     * @param sheetName 工作表名称
     * @return {@link FieldMappingResponse}
     */
    FieldMappingResponse getFieldMapping(String fileName, String sheetName);

    /**
     * 文件导入数据
     *
     * @param request 文件导入数据请求
     * @return {@link FileImportDataResponse}
     */
    FileImportDataResponse importData(FileImportDataRequest request);
}
