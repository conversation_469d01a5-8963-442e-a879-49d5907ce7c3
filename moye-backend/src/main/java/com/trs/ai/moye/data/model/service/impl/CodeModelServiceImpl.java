package com.trs.ai.moye.data.model.service.impl;

import com.trs.ai.moye.batchengine.entity.BatchEngineTaskParam;
import com.trs.ai.moye.batchengine.service.BatchEngineService;
import com.trs.ai.moye.common.utils.BeanUtil;
import com.trs.ai.moye.data.connection.request.SubtaskExecutionRequest;
import com.trs.ai.moye.data.model.dao.BatchArrangementMapper;
import com.trs.ai.moye.data.model.dao.BatchCodeHistoryMapper;
import com.trs.ai.moye.data.model.entity.BatchArrangement;
import com.trs.ai.moye.data.model.entity.BatchCodeHistory;
import com.trs.ai.moye.data.model.entity.CodeSubTasks;
import com.trs.ai.moye.data.model.enums.ArrangeDisplayType;
import com.trs.ai.moye.data.model.request.BatchCodeRequest;
import com.trs.ai.moye.data.model.request.CodeFormatterRequest;
import com.trs.ai.moye.data.model.response.BatchCodeResponse;
import com.trs.ai.moye.data.model.service.CodeModelService;
import com.trs.ai.moye.data.model.task.start.TaskStart;
import com.trs.ai.moye.permission.service.CurrentUserService;
import com.trs.ai.moye.permission.service.DynamicUserNameService;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.utils.SnowflakeIdUtil;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.BatchProcessSparkConfig;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.enums.ModelExecuteStatus;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 代码模式
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/10/22 18:20
 **/
@Service
@Slf4j
public class CodeModelServiceImpl implements CodeModelService {

    @Resource
    private BatchArrangementMapper batchArrangementMapper;

    @Resource
    private DataModelMapper dataModelMapper;

    @Resource
    private TaskStart taskStart;

    @Resource
    private CurrentUserService currentUserService;

    @Resource
    private BatchEngineService batchEngineService;

    @Resource
    private BatchCodeHistoryMapper batchCodeHistoryMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveCode(Integer dataModelId, BatchCodeRequest request) {
        BatchArrangement batchArrangement = batchArrangementMapper.selectByDataModelId(dataModelId);
        if (Objects.isNull(batchArrangement)) {
            throw new BizException(String.format("%s[dataModelId:%s]不存在", ModelLayer.DWD.getLabel(), dataModelId));
        }
        // 检查是否为代码模式
        if (!ArrangeDisplayType.CODE.equals(batchArrangement.getDisplayType())) {
            throw new BizException(
                String.format("%s[dataModelId:%s]模式为[displayType:%s]，须为代码模式", ModelLayer.DWD.getLabel(),
                    dataModelId,
                    batchArrangement.getDisplayType()));
        }
        // 保存代码历史
        saveCodeHistory(batchArrangement);

        List<CodeSubTasks> codeSubTasks = request.toCodeConfig();
        batchArrangement.setCodeSubTasks(codeSubTasks.toArray(new CodeSubTasks[0]));
        batchArrangement.setIsUpdatedTasks(request.getIsUpdatedTasks());
        batchArrangementMapper.updateById(batchArrangement);
        dataModelMapper.updateIsArranged(dataModelId, true);

        DataModel dataModel = dataModelMapper.selectById(dataModelId);
        // 任务为暂停或者启动状态，并且代码更新了就停止
        if ((ModelExecuteStatus.START.equals(dataModel.getExecuteStatus())
            || ModelExecuteStatus.PAUSE.equals(dataModel.getExecuteStatus())
        ) && request.getIsUpdatedTasks() == Boolean.TRUE) {
            // 暂停状态下，先启动任务
            if (ModelExecuteStatus.PAUSE.equals(dataModel.getExecuteStatus())) {
                taskStart.startTask(dataModelId, null);
            }
            taskStart.stopTask(dataModelId);
        }
        return Boolean.TRUE;
    }

    @Override
    public BatchCodeResponse getCode(Integer dataModelId) {
        BatchArrangement batchArrangement = batchArrangementMapper.selectByDataModelId(dataModelId);

        if (batchArrangement == null) {
            throw new BizException(
                String.format("%s[dataModelId:%s]没有批任务配置信息", ModelLayer.DWD.getLabel(), dataModelId));
        }
        if (!ArrangeDisplayType.CODE.equals(batchArrangement.getDisplayType())) {
            throw new BizException(
                String.format("%s[dataModelId:%s]模式为[displayType:%s]，须为代码模式", ModelLayer.DWD.getLabel(),
                    dataModelId,
                    batchArrangement.getDisplayType()));
        }
        return BatchCodeResponse.fromBatchCodeResponse(batchArrangement);
    }

    @Override
    public List<String> codeSubtaskExecute(SubtaskExecutionRequest request) {
        Integer userId = currentUserService.getUserId();
        if (Objects.isNull(userId)) {
            throw new BizException("当前用户已被删除！");
        }
        DataModel dataModel = dataModelMapper.selectById(request.getTaskId());

        BatchEngineTaskParam task = new BatchEngineTaskParam(String.valueOf(request.getTaskId()), dataModel.getLayer(),
            request.getTaskZhName());
        task.setCode(request.getCode());
        task.setSubTaskName(request.getSubtaskName());
        task.setCodeType(request.getCodeType());
        task.setUserId(userId);
        task.setExecuteId(buildExecuteId());
        BatchProcessSparkConfig config = request.getConfig();
        task.setSparkConfig(config);
        List<BatchEngineTaskParam> tasks = List.of(task);
        batchEngineService.executeCode(tasks);
        return tasks.stream().map(BatchEngineTaskParam::getExecuteId).toList();
    }

    /**
     * 构建执行id
     *
     * @return java.lang.String
     */
    public static synchronized String buildExecuteId() {
        return String.valueOf(SnowflakeIdUtil.newId());
    }


    @Override
    public List<String> codeExecute(Integer dataModelId, BatchProcessSparkConfig request) {

        // 检查 dataModelId 和 userId 是否为空，否则抛出异常
        Objects.requireNonNull(dataModelId, "dataModelId不能为空！");
        Integer userId = currentUserService.getUserId();
        Objects.requireNonNull(userId, "当前用户已被删除！");

        // 查询 BatchArrangement 和 DataModel
        BatchArrangement batchArrangement = batchArrangementMapper.selectByDataModelId(dataModelId);
        DataModel dataModel = dataModelMapper.selectById(dataModelId);

        // 检查 BatchArrangement 和 DataModel 是否为空，否则抛出异常
        Objects.requireNonNull(batchArrangement, "未找到可执行的任务配置信息！[dataModelId:" + dataModelId + "]");
        Objects.requireNonNull(dataModel, "未找到可执行的任务元数据信息！[dataModelId:" + dataModelId + "]");

        CodeSubTasks[] codeSubTasks = batchArrangement.getCodeSubTasks();
        Objects.requireNonNull(codeSubTasks, "未找到可执行的代码信息！[dataModelId:" + dataModelId + "]");
        List<BatchEngineTaskParam> tasks = Arrays.stream(codeSubTasks).map(codeSubTask -> {
            BatchEngineTaskParam vo = new BatchEngineTaskParam(String.valueOf(dataModelId), dataModel.getLayer(),
                dataModel.getZhName());
            vo.setCode(codeSubTask.getCode());
            vo.setSubTaskName(codeSubTask.getName());
            vo.setCodeType(codeSubTask.getCodeType());
            vo.setUserId(userId);
            vo.setSparkConfig(request);
            vo.setExecuteId(buildExecuteId());
            vo.setContinueOnException(request.getContinueOnException());
            return vo;
        }).toList();
        batchEngineService.executeCode(tasks);
        return tasks.stream().map(BatchEngineTaskParam::getExecuteId).toList();
    }

    @Override
    public String formatCode(CodeFormatterRequest request) {
        log.info("格式化代码：{}", request);
        if (Objects.isNull(request) || StringUtils.isEmpty(request.getCode())) {
            return request.getCode();
        }
        return batchEngineService.formatCode(request);
    }

    private void saveCodeHistory(BatchArrangement batchArrangement) {
        if (batchArrangement == null || batchArrangement.getCodeSubTasks() == null
            || batchArrangement.getCodeSubTasks().length == 0) {
            return;
        }
        BatchCodeHistory currentHistory = batchCodeHistoryMapper.selectByDataModelId(batchArrangement.getDataModelId());
        BatchCodeHistory history = new BatchCodeHistory(batchArrangement);
        if (currentHistory != null) {
            // 如果已经存在历史记录，则更新
            history.setId(currentHistory.getId());
            batchCodeHistoryMapper.updateById(history);
        } else {
            batchCodeHistoryMapper.insert(history);
        }
    }

    @Override
    public BatchCodeResponse getCodeHistory(Integer dataModelId) {
        BatchCodeHistory history = batchCodeHistoryMapper.selectByDataModelId(dataModelId);
        if (history == null) {
            return BatchCodeResponse.empty();
        }
        DynamicUserNameService service = BeanUtil.getBean(DynamicUserNameService.class);
        return BatchCodeResponse.fromBatchCodeResponse(service.getUserName(history.getUpdateBy()),
            history.getUpdateTime(), history.getCodeSubTasks(), true);
    }
}
