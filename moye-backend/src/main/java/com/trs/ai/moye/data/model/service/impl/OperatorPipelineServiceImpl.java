package com.trs.ai.moye.data.model.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.ai.moye.data.ability.dao.AbilityMapper;
import com.trs.ai.moye.data.model.dao.operator.OperatorNewMapper;
import com.trs.ai.moye.data.model.dao.operator.OperatorPipelineDraftMapper;
import com.trs.ai.moye.data.model.dao.operator.OperatorPipelineMapper;
import com.trs.ai.moye.data.model.dto.arrangement.TestPipelineRequest;
import com.trs.ai.moye.data.model.dto.arrangement.stream.OperatorDto;
import com.trs.ai.moye.data.model.dto.arrangement.stream.OperatorPipelineDTO;
import com.trs.ai.moye.data.model.service.OperatorPipelineService;
import com.trs.ai.moye.storageengine.feign.MqConnectionFeign;
import com.trs.ai.moye.streamengine.feign.StreamEngineFeign;
import com.trs.moye.ability.base.storage.StorageAbility;
import com.trs.moye.ability.domain.DataProcessRecord;
import com.trs.moye.ability.domain.DataProcessResult;
import com.trs.moye.ability.domain.TestDataProcessRequest;
import com.trs.moye.ability.entity.Ability;
import com.trs.moye.ability.entity.InputBind;
import com.trs.moye.ability.entity.OutputBind;
import com.trs.moye.ability.entity.operator.Operator;
import com.trs.moye.ability.entity.operator.OperatorCanvas;
import com.trs.moye.ability.entity.operator.OperatorPipeline;
import com.trs.moye.ability.entity.operator.OperatorRowType;
import com.trs.moye.ability.entity.operator.OperatorViewInfo;
import com.trs.moye.ability.entity.operator.StorageOperatorConfig;
import com.trs.moye.ability.enums.AbilityType;
import com.trs.moye.base.common.response.ResponseMessage;
import com.trs.moye.base.common.utils.DataProcessUtils;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.data.connection.entity.params.ConnectionParams;
import com.trs.moye.base.data.connection.entity.params.KafkaConnectionParams;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.connection.enums.DataSourceCategory;
import com.trs.moye.base.data.model.dao.DataModelFieldMapper;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.model.entity.DataSourceConfig;
import com.trs.moye.base.data.model.enums.CreateTableStatus;
import com.trs.moye.base.data.source.dao.DataSourceConfigMapper;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.data.storage.dao.DataStorageMapper;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.lang3.ObjectUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 数据治理算子编排Service实现
 *
 * <AUTHOR>
 * @since 2025/03/11 16:10:01
 */
@Service
public class OperatorPipelineServiceImpl implements OperatorPipelineService {

    @Resource
    private OperatorPipelineMapper operatorPipelineMapper;

    @Resource
    private OperatorNewMapper operatorNewMapper;

    @Resource
    private ArrangementFieldService arrangementFieldService;

    @Resource
    private AbilityMapper abilityMapper;

    @Resource
    private DataModelFieldMapper dataModelFieldMapper;

    @Resource
    private DataModelMapper dataModelMapper;

    @Resource
    private DataStorageMapper dataStorageMapper;

    @Resource
    private StreamEngineFeign streamEngineFeign;

    @Resource
    private OperatorPipelineDraftMapper operatorPipelineDraftMapper;

    @Resource
    private MqConnectionFeign mqConnectionFeign;

    @Resource
    private DataSourceConfigMapper dataSourceConfigMapper;

    @Value("${stream.pipeline-test.default-fetch-message-count:10}")
    private Integer defaultFetchMessageCount;


    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "stream-engine:execute:base-info", key = "#dataModelId")
    public void saveOperatorPipeline(Integer dataModelId, OperatorPipelineDTO operatorPipelineDTO) {
        // 同步算子字段到当前要素库
        updateFieldsToDataModel(dataModelId, operatorPipelineDTO.getOperators(), operatorPipelineDTO.getInputFields());
        //删除旧编排关系
        this.deleteOperatorPipeline(dataModelId);
        OperatorPipeline operatorPipeline = new OperatorPipeline();
        operatorPipeline.setDataModelId(Long.valueOf(operatorPipelineDTO.getDataModelId()));
        operatorPipeline.setDataSourceConnectionId(operatorPipelineDTO.getDataSourceConnectionId());
        operatorPipelineMapper.insert(operatorPipeline);
        Long pipelineId = operatorPipeline.getId();

        // 自动生成存储算子
        List<OperatorDto> operatorDtos = operatorPipelineDTO.getOperators();
        operatorDtos.addAll(createStorageOperator(
            extractStorageTableViewInfosFromCanvas(operatorPipelineDTO.getCanvas())
        ));
        // 批量插入
        if (ObjectUtils.isNotEmpty(operatorDtos)) {
            // 设置pipelineId
            operatorDtos.forEach(dto -> {
                dto.setPipelineId(pipelineId);
                dto.setDataModelId(dataModelId);
            });
            operatorNewMapper.batchInsert(operatorDtos);
        }

        // 创建displayId到operatorDto的映射
        Map<Long, Integer> displayIdToOperatorIdMap = operatorDtos.stream()
            .filter(e -> Objects.nonNull(e.getDisplayId()))
            .collect(Collectors.toMap(OperatorDto::getDisplayId, OperatorDto::getId));

        operatorPipeline.setFieldMapping(operatorPipelineDTO.getFieldMapping());
        operatorPipeline.setInputFields(operatorPipelineDTO.getInputFields());

        // 更新视图信息中的operatorId
        OperatorCanvas canvas = operatorPipelineDTO.getCanvas();
        if (Objects.nonNull(canvas) && Objects.nonNull(canvas.getOperatorViewInfo())) {
            Arrays.stream(canvas.getOperatorViewInfo())
                .filter(info -> displayIdToOperatorIdMap.containsKey(info.getDisplayId()))
                .forEach(info -> info.setOperatorId(displayIdToOperatorIdMap.get(info.getDisplayId())));
            // 更新画布和编排信息
            operatorPipeline.setCanvas(canvas);
            operatorPipelineMapper.updateById(operatorPipeline);
        }
        //更新建模是否编排状态
        dataModelMapper.updateIsArranged(dataModelId, true);

        // 删除草稿
        operatorPipelineDraftMapper.deleteByDataModelId(dataModelId);
    }

    /**
     * 从画布中提取所有存储表视图信息
     *
     * @param canvas 算子画布
     * @return 存储表视图信息列表
     */
    private List<OperatorViewInfo.StorageTableViewInfo> extractStorageTableViewInfosFromCanvas(OperatorCanvas canvas) {
        if (canvas == null || canvas.getOperatorViewInfo() == null) {
            return Collections.emptyList();
        }

        return Arrays.stream(canvas.getOperatorViewInfo())
            .filter(operatorViewInfo -> operatorViewInfo instanceof OperatorViewInfo.StorageTableViewInfo)
            .map(info -> (OperatorViewInfo.StorageTableViewInfo) info)
            .toList();
    }

    @Override
    public OperatorPipelineDTO getOperatorPipeline(Integer dataModelId) {
        return Optional.ofNullable(operatorPipelineMapper.selectByDataModelId(dataModelId))
            .map(pipeline -> buildPipelineDTO(dataModelId, pipeline))
            .orElseGet(OperatorPipelineDTO::new);
    }

    @Override
    public List<DataProcessRecord> testOperatorPipeline(Integer dataModelId, TestPipelineRequest request) {
        OperatorPipelineDTO pipeline = request.getPipeline();
        TestDataProcessRequest dataProcessRequest = new TestDataProcessRequest();
        dataProcessRequest.setInputFields(pipeline.getInputFields());
        dataProcessRequest.setFieldMapping(pipeline.getFieldMapping());
        //过滤掉存储算子
        List<Operator> operators = pipeline.getOperators().stream()
            .filter(operator -> {
                Ability ability = abilityMapper.selectById(operator.getAbilityId());
                operator.setAbility(ability);
                return !ability.getType().equals(AbilityType.STORAGE);
            })
            .collect(Collectors.toList());
        dataProcessRequest.setOperators(operators);
        dataProcessRequest.setMsg(request.getMsg());
        ResponseMessage response = streamEngineFeign.test(dataModelId, dataProcessRequest);
        List<DataProcessResult> dataProcessResults = JsonUtils.toList(JsonUtils.toJsonString(response.getData()),
            DataProcessResult.class);
        return dataProcessResults.stream().map(result -> {
            DataProcessRecord record = result.getRecord();
            record.setTraces(result.getTraces());
            return record;
        }).collect(Collectors.toList());
    }

    @Override
    public List<JsonNode> getMessage(Integer dataModelId, Integer dataSourceConnectionId) {
        List<DataSourceConfig> dataSources = dataSourceConfigMapper.selectByDataModel(dataModelId);
        if (dataSources.size() == 1) {
            DataSourceConfig dataSource = dataSources.get(0);
            DataModel dataModel = dataModelMapper.selectById(dataSource.getSourceModelId());
            return mqConnectionFeign.getMessage(dataSourceConnectionId, dataModel.getEnName(), defaultFetchMessageCount);
        } else {
            throw new IllegalStateException("数据模型关联的数据来源不唯一，请检查配置。");
        }
    }

    private OperatorPipelineDTO buildPipelineDTO(Integer dataModelId, OperatorPipeline pipeline) {
        OperatorPipelineDTO dto = new OperatorPipelineDTO();
        dto.setId(pipeline.getId());
        dto.setDataModelId(dataModelId);
        dto.setCanvas(pipeline.getCanvas());
        dto.setInputFields(pipeline.getInputFields());
        dto.setFieldMapping(pipeline.getFieldMapping());
        dto.setDataSourceConnectionId(pipeline.getDataSourceConnectionId());
        List<Operator> operators = operatorNewMapper.selectByPipelineId(pipeline.getId());
        dto.setOperators(sortOperators(operators, dto.getCanvas()));
        return dto;
    }

    /**
     * 对算子进行排序
     *
     * @param operators 算子列表
     * @param canvas    画布信息
     * @return {@link List }<{@link OperatorDto }>
     */
    private List<OperatorDto> sortOperators(List<? extends Operator> operators, OperatorCanvas canvas) {
        if (canvas == null || canvas.getOperatorViewInfo() == null) {
            return operators.stream()
                .map(OperatorDto::new)
                .toList();
        }

        Map<Integer, Integer> orderMapping = createOrderMapping(canvas.getOperatorViewInfo());
        return operators.stream()
            .sorted(createOrderComparator(orderMapping))
            .map(OperatorDto::new)
            .toList();
    }

    private Map<Integer, Integer> createOrderMapping(OperatorViewInfo[] viewInfos) {
        return Arrays.stream(viewInfos)
            .filter(info -> info.getOperatorId() != null && info.getOrder() != null)
            .collect(Collectors.toUnmodifiableMap(
                OperatorViewInfo::getOperatorId,
                OperatorViewInfo::getOrder,
                (existing, current) -> existing
            ));
    }

    private Comparator<Operator> createOrderComparator(Map<Integer, Integer> orderMapping) {
        return Comparator.comparingInt(operator ->
            orderMapping.getOrDefault(operator.getId(), Integer.MAX_VALUE)
        );
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteOperatorPipeline(Integer dataModelId) {
        // 查询算子编排基本信息
        OperatorPipeline operatorPipeline = operatorPipelineMapper.selectByDataModelId(dataModelId);
        if (Objects.isNull(operatorPipeline)) {
            return false;
        }
        // 删除关联的所有算子
        operatorNewMapper.deleteByPipelineId(operatorPipeline.getId());
        // 删除算子编排记录
        operatorPipelineMapper.deleteById(operatorPipeline.getId());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOperatorPipelineDraft(Integer dataModelId, OperatorPipelineDTO operatorPipelineDTO) {
        operatorPipelineDraftMapper.deleteByDataModelId(dataModelId);
        operatorPipelineDraftMapper.insert(operatorPipelineDTO);
    }

    @Override
    public OperatorPipelineDTO getOperatorPipelineDraft(Integer dataModelId) {
        OperatorPipelineDTO dto = operatorPipelineDraftMapper.selectByDataModelId(dataModelId);
        if (dto == null) {
            return new OperatorPipelineDTO();
        } else {
            // 草稿算子不入库，若没有算子id则用顺序作为id
            OperatorViewInfo[] operatorViewInfo = dto.getCanvas().getOperatorViewInfo();
            for (int i = 1; i <= dto.getOperators().size(); i++) {
                OperatorDto operator = dto.getOperators().get(i - 1);
                if (operator.getId() == null) {
                    operator.setAbility(abilityMapper.selectById(operator.getAbilityId()));
                    //防止已保存过的id和自动生成id重复，用负数
                    operator.setId(-i);
                    for (OperatorViewInfo viewInfo : operatorViewInfo) {
                        if (viewInfo.getDisplayId().equals(operator.getDisplayId())) {
                            viewInfo.setOperatorId(-i);
                            break;
                        }
                    }
                }
            }
            return dto;
        }
    }

    private void updateFieldsToDataModel(Integer dataModelId, List<OperatorDto> operators,
        OperatorRowType inputFields) {
        // 1. 找出存在的字段
        Set<String> existingFieldNames = getExistingFieldNames(dataModelId);

        // 2. 创建并添加字段
        List<DataModelField> newFields = createFieldsFromOperators(operators, inputFields, existingFieldNames);
        if (!newFields.isEmpty()) {
            arrangementFieldService.addDataModelFields(dataModelId, newFields);
        }
    }

    /**
     * 获取已存在的数据模型字段名称
     *
     * @param dataModelId 数据建模id
     * @return {@link Set }<{@link String }>
     */
    private Set<String> getExistingFieldNames(Integer dataModelId) {
        return dataModelFieldMapper.selectByDataModelId(dataModelId).stream()
            .map(DataModelField::getEnName)
            .collect(Collectors.toSet());
    }

    /**
     * 根据算子能力创建字段
     *
     * @param operators   算子
     * @param inputFields 输入字段
     * @param existFields 缺少字段
     * @return {@link List }<{@link DataModelField }>
     */
    private List<DataModelField> createFieldsFromOperators(List<OperatorDto> operators, OperatorRowType inputFields,
        Set<String> existFields) {
        Map<String, DataModelField> fieldMap = new LinkedHashMap<>();
        operators.forEach(op -> {
            OperatorRowType output = op.getOutputFields();
            List<DataModelField> outputFields = output.toFields();
            for (DataModelField field : outputFields) {
                if (!existFields.contains(field.getEnName())) {
                    fieldMap.put(field.getEnName(), field);
                }
            }
        });
        inputFields.toFields().forEach(field -> {
            if (!existFields.contains(field.getEnName())) {
                fieldMap.put(field.getEnName(), field);
            }
        });

        return new ArrayList<>(fieldMap.values());
    }

    /**
     * 根据建模的存储点信息，自动生成存储算子
     *
     * @param storageInfoList 存储信息列表
     * @return 存储算子列表
     */
    private List<OperatorDto> createStorageOperator(List<OperatorViewInfo.StorageTableViewInfo> storageInfoList) {
        // 创建存储点的映射关系
        Map<Integer, StorageOperatorConfig> storageIdToConfigMap = new HashMap<>();

        // 收集每个存储点对应的配置信息
        storageInfoList.stream()
            .filter(info -> ObjectUtils.isNotEmpty(info.getStorageOperatorConfigs()))
            .forEach(info -> info.getStorageOperatorConfigs().forEach(config ->
                storageIdToConfigMap.put(config.getStorageId(), config)
            ));

        // 获取存储点id列表
        List<Integer> storageIds = new ArrayList<>(storageIdToConfigMap.keySet());

        List<DataStorage> storages = dataStorageMapper.selectByIdsWithConnection(storageIds).stream()
            .filter(dataStorage -> dataStorage.getCreateTableStatus().equals(CreateTableStatus.SUCCESS))
            .toList();

        // 创建存储算子
        return buildStorageOperators(storages, storageIdToConfigMap);
    }

    /**
     * 构建存储算子
     *
     * @param storages             存储
     * @param storageIdToConfigMap 存储点id到配置的映射
     * @return {@link List }<{@link OperatorDto }>
     */
    @NotNull
    private List<OperatorDto> buildStorageOperators(
        List<DataStorage> storages,
        Map<Integer, StorageOperatorConfig> storageIdToConfigMap) {
        Ability seatunnelStorageAbility = abilityMapper.selectByEnName(StorageAbility.SEATUNNEL_STORAGE_ABILITY);
        Ability mqStorageAbility = abilityMapper.selectByEnName(StorageAbility.MQ_STORAGE_ABILITY);
        List<OperatorDto> operators = new ArrayList<>();
        for (DataStorage storage : storages) {
            StorageOperatorConfig config = storageIdToConfigMap.get(storage.getId());
            if (Objects.isNull(config)) {
                continue;
            }
            List<DataModelField> fields = dataModelFieldMapper.selectByDataModelId(storage.getDataModelId());

            //判断是否有主键
            boolean hasPrimaryKey = !Objects.isNull(fields) && !fields.isEmpty() && fields.stream().anyMatch(DataModelField::isPrimaryKey);

            OperatorRowType storageFields = config.getStorageFields();
            InputBind inputBind = new InputBind()
                .addFixedValueBinding("fields", storageFields != null ? storageFields.keySet() : new HashSet<>())
                .addFixedValueBinding("flatMapSwitch", config.getFlatMapSwitch())
                .addFixedValueBinding("flatMapFields", config.getFlatMapFields())
                .addFixedValueBinding("hasPrimaryKey", hasPrimaryKey);
            Ability ability;
            ConnectionType connectionType = storage.getConnection().getConnectionType();
            if (connectionType.getCategory().equals(DataSourceCategory.DATA_BASE)) {
                ability = seatunnelStorageAbility;
                String topicName = DataProcessUtils.buildDataStorageTopic(storage.getDataModelId(),
                    storage.getId());
                inputBind.addFixedValueBinding("topic", topicName);
            } else {
                ability = mqStorageAbility;
                ConnectionParams connectionParams = storage.getConnection().getConnectionParams();
                inputBind.addFixedValueBinding("connectionType", connectionType);
                inputBind.addFixedValueBinding("host", connectionParams.getHost());
                inputBind.addFixedValueBinding("port", connectionParams.getPort());
                if (ObjectUtils.isNotEmpty(connectionParams.getUsername())) {
                    inputBind.addFixedValueBinding("username", connectionParams.getUsername());
                }
                if (ObjectUtils.isNotEmpty(connectionParams.getPassword())) {
                    inputBind.addFixedValueBinding("password", connectionParams.getPassword());
                }
                if (connectionParams instanceof KafkaConnectionParams kafkaConnectionInfo
                    && kafkaConnectionInfo.getSaslMechanism() != null) {
                    inputBind.addFixedValueBinding("saslMechanism", kafkaConnectionInfo.getSaslMechanism());
                }
                inputBind.addFixedValueBinding("topic", storage.getEnName());
            }
            OutputBind outputBind = new OutputBind().addRootBinding();
            OperatorDto operator = new OperatorDto(ability.getId(), ability.getZhName(), inputBind, outputBind,
                storage.getId(), storage.getDataModelId(), config.getConditions()
            );
            operators.add(operator);
        }
        return operators;
    }

}
