package com.trs.ai.moye.data.model.response;

import com.trs.ai.moye.common.response.TreeBaseResponse;
import com.trs.ai.moye.data.model.entity.MetadataRelation;
import com.trs.ai.moye.data.model.enums.CategoryTreeNodeType;
import com.trs.moye.base.data.model.entity.BusinessCategory;
import com.trs.moye.base.data.model.entity.DataModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 层级树返回
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/9/26 16:46
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CategoryTreeResponse extends TreeBaseResponse {


    /**
     * 每个层次的类型
     */
    private CategoryTreeNodeType nodeType;

    /**
     * 英文名称
     */
    private String enName;

    /**
     * 图标名称
     */
    private String iconName;

    /**
     * 排序
     */
    private Integer order;

    public CategoryTreeResponse(CategoryTreeNodeType nodeType, String enName, String iconName) {
        this.setNodeType(nodeType);
        this.setEnName(enName);
        this.setIconName(iconName);
    }

    public CategoryTreeResponse(BusinessCategory businessCategory) {
        this.setNodeType(CategoryTreeNodeType.BUSINESS);
        this.setEnName(businessCategory.getEnName());
        this.setIconName("iconName");
        this.setId(businessCategory.getId());
        this.setPid(0);
        this.setName(businessCategory.getZhName());
        //第二层是固定的
    }

    public CategoryTreeResponse(MetadataRelation metadataRelation) {
        this.setNodeType(CategoryTreeNodeType.MODEL);
        this.setEnName(metadataRelation.getDataModelEnName());
        this.setId(metadataRelation.getDataModelId());
        this.setPid(metadataRelation.getCategoryId());
        this.setName(metadataRelation.getDataModelName());
    }


    public CategoryTreeResponse(DataModel model) {
        this.setNodeType(CategoryTreeNodeType.MODEL);
        this.setEnName(model.getEnName());
        this.setId(model.getId());
        this.setPid(model.getLayer().ordinal());
        this.setName(model.getZhName());
    }
}
