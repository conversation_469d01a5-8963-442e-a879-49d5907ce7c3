package com.trs.ai.moye.data.model.request;

import com.trs.moye.base.common.enums.ModelLayer;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-08-06 11:29
 */
@Data
public class ThemeSelectSourceRequest {

    private List<Integer> businessCategoryIds;

    private ModelLayer modelLayer;

    private Boolean filterNotCreatedTables;

    /**
     * 获取当前层级及下层枚举
     *
     * @return 枚举值列表
     */
    public List<ModelLayer> getModelLayers() {
        List<ModelLayer> layers = new ArrayList<>();
        if (modelLayer == null) {
            return layers;
        }
        for (int i = 0; i <= modelLayer.ordinal(); i++) {
            layers.add(ModelLayer.values()[i]);
        }
        return layers;
    }
}
