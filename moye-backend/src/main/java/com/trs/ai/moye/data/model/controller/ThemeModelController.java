package com.trs.ai.moye.data.model.controller;

import com.trs.ai.moye.data.model.request.ThemeCreateRequest;
import com.trs.ai.moye.data.model.request.ThemeSelectSourceRequest;
import com.trs.ai.moye.data.model.response.CategoryTreeResponse;
import com.trs.ai.moye.data.model.service.ThemeModelService;
import com.trs.moye.base.data.model.entity.AggregationTableArrangement;
import com.trs.moye.base.data.model.request.arrangement.theme.ThemeArrangeRequest;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2025-07-31 13:55
 */
@Slf4j
@RestController
@RequestMapping("/data-model/theme")
@Validated
public class ThemeModelController {

    @Resource
    private ThemeModelService themeModelService;

    /**
     * 获取主题库建模来源列表
     *
     * @param request 请求参数
     * @return 来源列表
     */
    @PostMapping("/data-source/list")
    public List<CategoryTreeResponse> listModelSources(@RequestBody ThemeSelectSourceRequest request) {
        return themeModelService.listModelSources(request);
    }

    /**
     * 查询编排信息，并设置建表状态
     *
     * @param id 数据模型id
     * @return 编排信息
     */
    @GetMapping("/{id}/arrange")
    public AggregationTableArrangement getArrange(@PathVariable Integer id) {
        return themeModelService.getArrange(id);
    }

    /**
     * 添加主题库
     *
     * @param request 请求参数
     * @return 建模id
     */
    @PostMapping
    public int addTheme(@Validated @RequestBody ThemeCreateRequest request) {
        return themeModelService.addTheme(request);
    }

    /**
     * 更新主题库编排信息
     *
     * @param id      主题库id
     * @param request 请求参数
     */
    @PutMapping("/{id}/arrange")
    public void updateArrange(@PathVariable Integer id, @Validated @RequestBody ThemeArrangeRequest request) {
        themeModelService.updateArrange(id, request);
    }

}
