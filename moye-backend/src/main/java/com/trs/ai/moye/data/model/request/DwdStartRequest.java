package com.trs.ai.moye.data.model.request;

import com.trs.moye.base.data.execute.ExecuteParams;
import com.trs.moye.base.data.model.entity.CodeParameterItem;
import com.trs.moye.base.data.model.entity.SparkConfigItem;
import java.util.List;
import lombok.Data;

/**
 * 要素库启动请求
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/10 19:22
 **/
@Data
public class DwdStartRequest {

    /**
     * 凭证id
     */
    private Integer certificateId;

    /**
     * spark配置项列表，批处理任务使用
     */
    private List<SparkConfigItem> sparkConfigItemList;

    /**
     * 自定义可替换参数
     */
    private List<CodeParameterItem> customCodeParameters;

    /**
     * 遇到异常, 是否继续子任务
     * true: 继续执行后续子任务
     * false: 停止执行后续子任务
     */
    private Boolean continueOnException;

    /**
     * 执行参数：执行参数
     */
    private ExecuteParams executeParams;

}
