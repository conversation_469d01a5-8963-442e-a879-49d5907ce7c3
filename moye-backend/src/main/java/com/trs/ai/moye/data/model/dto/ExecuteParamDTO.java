package com.trs.ai.moye.data.model.dto;

import com.trs.ai.moye.data.model.request.DwdStartRequest;
import com.trs.moye.base.data.execute.ExecuteParams;
import com.trs.moye.base.data.model.entity.BatchProcessSparkConfig;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024-10-18 15:56
 */
@Data
@NoArgsConstructor
public class ExecuteParamDTO {

    /**
     * 执行参数：执行参数
     */
    private ExecuteParams executeParams;

    /**
     * 批处理的spark配置
     */
    private BatchProcessSparkConfig sparkConfig;

    public ExecuteParamDTO(ExecuteParams executeParams) {
        this.executeParams = executeParams;
        this.sparkConfig = null;
    }

    public ExecuteParamDTO(DwdStartRequest dwdStartRequest) {
        this.executeParams = dwdStartRequest.getExecuteParams();
        this.sparkConfig = new BatchProcessSparkConfig(dwdStartRequest.getCertificateId(),
            dwdStartRequest.getSparkConfigItemList(), dwdStartRequest.getCustomCodeParameters(), dwdStartRequest.getContinueOnException());
    }
}
