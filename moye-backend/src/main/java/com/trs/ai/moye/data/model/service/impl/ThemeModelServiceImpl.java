package com.trs.ai.moye.data.model.service.impl;

import com.trs.ai.moye.backstage.dao.NoticeSendConfInsideMapper;
import com.trs.ai.moye.backstage.entity.NoticeSendConfInside;
import com.trs.ai.moye.common.utils.BeanUtil;
import com.trs.ai.moye.data.ability.dao.AbilityMapper;
import com.trs.ai.moye.data.ability.service.AbilitySchemaHelper;
import com.trs.ai.moye.data.model.dao.BatchArrangementMapper;
import com.trs.ai.moye.data.model.dao.batch.BatchOperatorMapper;
import com.trs.ai.moye.data.model.dto.arrangement.BatchOperatorDetailVO;
import com.trs.ai.moye.data.model.entity.BatchArrangement;
import com.trs.ai.moye.data.model.entity.CodeSubTasks;
import com.trs.ai.moye.data.model.enums.ArrangeDisplayType;
import com.trs.ai.moye.data.model.request.ThemeCreateRequest;
import com.trs.ai.moye.data.model.request.ThemeSelectSourceRequest;
import com.trs.ai.moye.data.model.response.AggregationTableArrangementResponse;
import com.trs.ai.moye.data.model.response.CategoryTreeResponse;
import com.trs.ai.moye.data.model.response.DataModelTreeResponse;
import com.trs.ai.moye.data.model.response.LayerCategoryTreeResponse;
import com.trs.ai.moye.data.model.service.DataModelScheduleService;
import com.trs.ai.moye.data.model.service.DataModelService;
import com.trs.ai.moye.data.model.service.ThemeModelService;
import com.trs.ai.moye.data.model.service.impl.theme.DataUnionOperatorParseContext;
import com.trs.ai.moye.data.model.service.impl.theme.OperatorParseContext;
import com.trs.ai.moye.data.model.service.impl.theme.TableJoinOperatorParseContext;
import com.trs.moye.ability.entity.operator.BatchOperator;
import com.trs.moye.base.common.entity.AuditBaseEntity;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.help.AddUpdateDeleteSeparator;
import com.trs.moye.base.common.utils.AssertUtils;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.common.utils.ValidationUtils;
import com.trs.moye.base.data.model.dao.AggregationTableArrangementMapper;
import com.trs.moye.base.data.model.dao.BusinessCategoryMapper;
import com.trs.moye.base.data.model.dao.DataModelExecuteConfigMapper;
import com.trs.moye.base.data.model.dao.DataModelFieldMapper;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.AggregationTableArrangement;
import com.trs.moye.base.data.model.entity.BusinessCategory;
import com.trs.moye.base.data.model.entity.Canvas;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataModelExecuteConfig;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.model.entity.DataSourceConfig;
import com.trs.moye.base.data.model.enums.AggregationType;
import com.trs.moye.base.data.model.request.arrangement.theme.AggregationField;
import com.trs.moye.base.data.model.request.arrangement.theme.ArrangeAggregationTable;
import com.trs.moye.base.data.model.request.arrangement.theme.ArrangeSourceTable;
import com.trs.moye.base.data.model.request.arrangement.theme.SourceField;
import com.trs.moye.base.data.model.request.arrangement.theme.ThemeArrangeRequest;
import com.trs.moye.base.data.source.dao.DataSourceConfigMapper;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.data.storage.dao.DataStorageMapper;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2025-07-31 13:39
 */
@Slf4j
@Setter
@Service
public class ThemeModelServiceImpl implements ThemeModelService {

    @Resource
    private DataModelMapper dataModelMapper;
    @Resource
    private BusinessCategoryMapper businessCategoryMapper;
    @Resource
    private DataModelScheduleService dataModelScheduleService;
    @Resource
    private DataModelService dataModelService;
    @Resource
    private DataModelExecuteConfigMapper dataModelExecuteConfigMapper;
    @Resource
    private DataSourceConfigMapper dataSourceConfigMapper;
    @Resource
    private AggregationTableArrangementMapper aggregationTableArrangementMapper;
    @Resource
    private NoticeSendConfInsideMapper noticeSendConfInsideMapper;
    @Resource
    private DataModelFieldMapper dataModelFieldMapper;
    @Resource
    private DataStorageMapper dataStorageMapper;
    @Resource
    private AbilitySchemaHelper abilitySchemaHelper;
    @Resource
    private AbilityMapper abilityMapper;
    @Resource
    private BatchOperatorMapper batchOperatorMapper;
    @Resource
    private BatchArrangementMapper batchArrangementMapper;
    @Resource
    private ApplicationContext applicationContext;

    @Override
    public List<CategoryTreeResponse> listModelSources(ThemeSelectSourceRequest request) {
        List<BusinessCategory> businessCategoryList = businessCategoryMapper.getAllCategory();
        Map<Integer, BusinessCategory> businessCategoryMap = businessCategoryList.stream()
            .collect(Collectors.toMap(AuditBaseEntity::getId, Function.identity()));
        List<DataModel> dataModels = dataModelMapper.listByThemeModelSources(request.getBusinessCategoryIds(),
            request.getModelLayers(), request.getFilterNotCreatedTables());
        Map<Integer, Map<ModelLayer, List<DataModel>>> dataModelMapMap = dataModels.stream().collect(
            Collectors.groupingBy(DataModel::getBusinessCategoryId, Collectors.groupingBy(DataModel::getLayer)));
        // 业务分类节点列表
        List<CategoryTreeResponse> categoryNodes = new ArrayList<>();
        dataModelMapMap.forEach((categoryId, layerModelGroup) -> {
            BusinessCategory businessCategory = businessCategoryMap.get(categoryId);
            AssertUtils.notNull(businessCategory, "主键为[%s]业务分类不存在", categoryId);
            CategoryTreeResponse categoryNode = new CategoryTreeResponse(businessCategory);
            categoryNodes.add(categoryNode);
            // 层级节点列表
            List<LayerCategoryTreeResponse> layerNodes = new ArrayList<>();
            categoryNode.setChildren(layerNodes);
            for (ModelLayer layer : ModelLayer.values()) {
                List<DataModel> modelList = layerModelGroup.get(layer);
                if (ObjectUtils.isEmpty(modelList)) {
                    continue;
                }
                LayerCategoryTreeResponse layerNode = new LayerCategoryTreeResponse(layer, categoryId);
                layerNodes.add(layerNode);
                // 建模节点列表
                List<DataModelTreeResponse> modelNodes = new ArrayList<>();
                layerNode.setChildren(modelNodes);
                for (DataModel dataModel : modelList) {
                    DataModelTreeResponse modelNode = new DataModelTreeResponse(dataModel);
                    modelNodes.add(modelNode);
                }
            }
        });
        return categoryNodes;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int addTheme(ThemeCreateRequest request) {
        if (Boolean.TRUE.equals(dataModelMapper.existsByEnName(request.getBasicInfo().getEnName()))) {
            throw new BizException(String.format("数据建模英文名称[%s]已存在", request.getBasicInfo().getEnName()));
        }
        checkArrangeRequest(request.getArrangeInfo());
        DataModel dataModel = request.toDataModel();
        dataModelMapper.insert(dataModel);
        ThemeArrangeRequest arrangeInfo = request.getArrangeInfo();
        // 创建编排信息
        createArrangement(dataModel.getId(), arrangeInfo);
        // 创建来源
        Set<Integer> sourceModelIds = arrangeInfo.parseSourceModelIds();
        createDataSources(dataModel.getId(), sourceModelIds);
        // 创建字段
        createFields(dataModel.getId(), arrangeInfo);
        // 创建存储点
        dataModelService.createDataStorages(dataModel, request.getDataStorageIds());
        // 创建执行配置
        createExecuteConfig(dataModel.getId());
        // 创建后台消息中心日志推送配置
        BusinessCategory businessCategory = businessCategoryMapper.selectById(request.getBusinessCategoryId());
        noticeSendConfInsideMapper.insert(NoticeSendConfInside.formSystemAdd(dataModel, businessCategory));
        // 创建调度配置
        dataModelScheduleService.createScheduleConfig(dataModel, request.getScheduleInfo());
        return dataModel.getId();
    }

    private void checkArrangeRequest(ThemeArrangeRequest request) {
        if (request.getAggregationType() == AggregationType.TABLE_JOIN) {
            AssertUtils.notEmpty(request.getJoinRelations(), "聚合类型为表连接时，必须配置关联关系");
            request.getJoinRelations().forEach(ValidationUtils::validate);
            ArrangeAggregationTable aggregationTable = request.getAggregationTable();
            AssertUtils.notNull(aggregationTable, "聚合类型为表连接时，必须配置聚合表参数");
            ValidationUtils.validate(aggregationTable);
        } else if (request.getAggregationType() == AggregationType.DATA_UNION) {
            List<ArrangeSourceTable> sourceTables = request.getSourceTables();
            Set<String> table0SelectFields = sourceTables.get(0).getFields().stream().filter(SourceField::isChecked)
                .map(SourceField::getEnName).collect(Collectors.toSet());
            for (int i = 1; i < sourceTables.size(); i++) {
                Set<String> tableSelectFields = sourceTables.get(i).getFields().stream().filter(SourceField::isChecked)
                    .map(SourceField::getEnName).collect(Collectors.toSet());
                if (!table0SelectFields.equals(tableSelectFields)) {
                    throw new BizException("聚合表数据时，所有来源表的勾选字段必须一致");
                }
            }
        }
    }

    private void createFields(Integer dataModelId, ThemeArrangeRequest request) {
        List<DataModelField> fields = parseFields(dataModelId, request);
        dataModelFieldMapper.insert(fields);
    }

    private List<DataModelField> parseFields(Integer dataModelId, ThemeArrangeRequest request) {
        AggregationType aggregationType = request.getAggregationType();
        List<DataModelField> fields;
        if (aggregationType == AggregationType.TABLE_JOIN) {
            fields = parseTableJoinFields(dataModelId, request);
        } else {
            fields = parseDataUnionFields(dataModelId, request);
        }
        return fields;
    }

    private List<DataModelField> parseDataUnionFields(Integer dataModelId, ThemeArrangeRequest request) {
        ArrangeSourceTable sourceTable = request.getSourceTables().get(0);
        Map<String, DataModelField> modelFieldMap = dataModelFieldMapper.selectByDataModelId(
                sourceTable.getDataModelId())
            .stream().collect(Collectors.toMap(DataModelField::getEnName, Function.identity()));
        return request.getSourceTables().get(0).getFields().stream()
            .filter(SourceField::isChecked)
            .map(f -> buildModelField(dataModelId, modelFieldMap.get(f.getEnName()))).toList();
    }

    private List<DataModelField> parseTableJoinFields(Integer dataModelId, ThemeArrangeRequest request) {
        ArrangeAggregationTable table = request.getAggregationTable();
        List<AggregationField> tableFields = table.getFields();
        Set<Integer> storageIds = tableFields.stream().map(AggregationField::getStorageId).collect(Collectors.toSet());
        Map<Integer, DataStorage> storageMap = dataStorageMapper.selectBatchIds(storageIds).stream()
            .collect(Collectors.toMap(DataStorage::getId, Function.identity()));
        if (storageIds.size() != storageMap.size()) {
            Set<Integer> diff = storageIds.stream().filter(id -> !storageMap.containsKey(id))
                .collect(Collectors.toSet());
            throw new BizException("主键为：" + diff + "的存储点不存在，请检查来源表或聚合表存储点id是否正确");
        }
        Map<Integer, Map<String, DataModelField>> sourceModelFieldGroupMap = dataModelFieldMapper.selectByStorageIds(
                storageIds).stream()
            .collect(Collectors.groupingBy(DataModelField::getDataModelId,
                Collectors.toMap(DataModelField::getEnName, Function.identity())));
        List<DataModelField> fields = new ArrayList<>(tableFields.size());
        for (AggregationField tableField : tableFields) {
            DataStorage storage = storageMap.get(tableField.getStorageId());
            Map<String, DataModelField> sourceModelFieldGroup = sourceModelFieldGroupMap.get(
                storage.getDataModelId());
            DataModelField sourceModelField = sourceModelFieldGroup.get(tableField.getEnName());
            AssertUtils.notNull(sourceModelField, "聚合表的【%s】字段，对应来源表的【%s】字段，在来源表中不存在",
                tableField.getTargetZhName(), tableField.getEnName());
            fields.add(buildModelField(dataModelId, sourceModelField, tableField));
        }
        return fields;
    }

    private DataModelField buildModelField(Integer dataModelId, DataModelField sourceModelField,
        AggregationField tableField) {
        DataModelField field = new DataModelField();
        BeanUtil.copyInheritProperties(sourceModelField, field);
        field.setId(null);
        field.setEnName(tableField.getTargetEnName());
        field.setZhName(tableField.getTargetZhName());
        field.setDataModelId(dataModelId);
        return field;
    }

    private DataModelField buildModelField(Integer dataModelId, DataModelField sourceModelField) {
        DataModelField field = new DataModelField();
        BeanUtil.copyInheritProperties(sourceModelField, field);
        field.setId(null);
        field.setDataModelId(dataModelId);
        return field;
    }

    private void createExecuteConfig(Integer dataModelId) {
        DataModelExecuteConfig executeConfig = new DataModelExecuteConfig();
        executeConfig.setDataModelId(dataModelId);
        dataModelExecuteConfigMapper.insert(executeConfig);
    }

    private void createDataSources(Integer dataModelId, Collection<Integer> sourceModelIds) {
        List<DataModel> dataModels = dataModelMapper.selectByIds(sourceModelIds);
        List<DataSourceConfig> dataSourceConfigs = dataModels.stream().map(dm -> DataSourceConfig.from(dm, dataModelId))
            .toList();
        dataSourceConfigMapper.insert(dataSourceConfigs);
    }

    private void createArrangement(Integer dataModelId, ThemeArrangeRequest request) {
        AggregationTableArrangement arrangement = new AggregationTableArrangement(dataModelId, request);
        aggregationTableArrangementMapper.insert(arrangement);
        refreshBatchOperator(dataModelId, request);
    }

    private void refreshBatchOperator(Integer dataModelId, ThemeArrangeRequest request) {
        BatchArrangement batchArrangement = batchArrangementMapper.selectByDataModelId(dataModelId);
        if (batchArrangement == null) {
            batchArrangement = createBatchArrangement(dataModelId);
        }
        DataModel dataModel = dataModelMapper.getById(dataModelId);
        // 复制出新的请求对象，防止修改原始请求对象中的编排信息
        ThemeArrangeRequest newRequest = JsonUtils.parseObject(JsonUtils.toJsonString(request),
            ThemeArrangeRequest.class);
        assert newRequest != null;
        OperatorParseContext operatorContext;
        if (request.getAggregationType() == AggregationType.TABLE_JOIN) {
            operatorContext = applicationContext.getBean(TableJoinOperatorParseContext.class, dataModel,
                batchArrangement, newRequest);
        } else {
            operatorContext = applicationContext.getBean(DataUnionOperatorParseContext.class, dataModel,
                batchArrangement, newRequest);
        }
        // 更新批处理算子
        List<BatchOperator> operator = operatorContext.getOperator();
        batchOperatorMapper.deleteByDataModelId(dataModelId);
        batchOperatorMapper.insert(operator);
    }

    private BatchArrangement createBatchArrangement(Integer dataModelId) {
        BatchArrangement ba = new BatchArrangement();
        ba.setDataModelId(dataModelId);
        ba.setDisplayType(ArrangeDisplayType.CANVAS);
        ba.setArrangement(new BatchOperatorDetailVO[]{});
        ba.setCodeSubTasks(new CodeSubTasks[]{});
        ba.setCanvas(Canvas.getDefaultCanvas());
        ba.setIsUpdatedTasks(false);
        batchArrangementMapper.insert(ba);
        return ba;
    }

    @Override
    public AggregationTableArrangement getArrange(Integer id) {
        DataModel dataModel = dataModelMapper.getById(id);
        AssertUtils.notNull(dataModel, "数据模型不存在");
        AggregationTableArrangement arrangement = aggregationTableArrangementMapper.getByModelId(id);
        if (arrangement == null) {
            arrangement = new AggregationTableArrangement();
            arrangement.setCanvas(Canvas.getDefaultCanvas());
            arrangement.setDataModelId(id);
            return new AggregationTableArrangementResponse(arrangement, dataModel);
        }
        // 同步来源字段信息
        syncSourceFields(arrangement);
        // 设置建表状态
        setCreateTableStatus(id, arrangement);
        return new AggregationTableArrangementResponse(arrangement, dataModel);
    }

    private void syncSourceFields(AggregationTableArrangement arrangement) {
        List<ArrangeSourceTable> sourceTables = arrangement.getSourceTables();
        for (ArrangeSourceTable sourceTable : sourceTables) {
            List<DataModelField> modelFields = dataModelFieldMapper.selectByDataModelId(sourceTable.getDataModelId());
            List<SourceField> sourceFields = sourceTable.getFields();
            AddUpdateDeleteSeparator<DataModelField, String> separator = AddUpdateDeleteSeparator.create(
                sourceFields, SourceField::getEnName, modelFields, DataModelField::getEnName);
            List<DataModelField> addDataList = separator.getAddDataList();
            List<String> deleteKeyList = separator.getDeleteKeyList();
            addDataList.forEach(field -> sourceFields.add(new SourceField(field)));
            sourceFields.forEach(field -> field.setExist(!deleteKeyList.contains(field.getEnName())));
        }
    }

    private void setCreateTableStatus(Integer dataModelId, AggregationTableArrangement arrangement) {
        List<DataModelField> createTableFields = dataModelFieldMapper.selectCreateTableFields(dataModelId);
        Map<String, AggregationField> aggregationFieldMap = arrangement.getAggregationTable().getFields().stream()
            .collect(Collectors.toMap(AggregationField::getTargetEnName, Function.identity()));
        Map<Integer, Map<String, SourceField>> sourceFieldMapMap = arrangement.getSourceTables().stream()
            .collect(Collectors.toMap(ArrangeSourceTable::getStorageId,
                st -> st.getFields().stream().collect(Collectors.toMap(SourceField::getEnName, Function.identity()))));
        for (DataModelField ctf : createTableFields) {
            // 聚合字段设置建表状态
            AggregationField aggregationField = aggregationFieldMap.get(ctf.getEnName());
            if (aggregationField == null) {
                continue;
            }
            aggregationField.setCreateTable(true);
            // 对应来源字段设置建表状态
            Map<String, SourceField> sourceFieldMap = sourceFieldMapMap.get(aggregationField.getStorageId());
            if (sourceFieldMap == null) {
                continue;
            }
            SourceField sourceField = sourceFieldMap.get(aggregationField.getEnName());
            if (sourceField != null) {
                sourceField.setCreateTable(true);
            }
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateArrange(Integer id, ThemeArrangeRequest request) {
        AggregationTableArrangement oldArrangement = aggregationTableArrangementMapper.getByModelId(id);
        if (oldArrangement == null) {
            createArrangement(id, request);
            dataSourceConfigMapper.deleteByDataModelId(id);
            updateSourceConfig(id, request.parseSourceModelIds());
            // 刷新字段
            refreshFields(id, request);
            return;
        }
        AggregationTableArrangement arrangement = new AggregationTableArrangement(id, request);
        oldArrangement.setSourceTables(arrangement.getSourceTables());
        oldArrangement.setAggregationTable(arrangement.getAggregationTable());
        oldArrangement.setCanvas(arrangement.getCanvas());
        oldArrangement.setJoinRelations(arrangement.getJoinRelations());
        oldArrangement.setAggregationType(arrangement.getAggregationType());
        aggregationTableArrangementMapper.updateById(oldArrangement);
        // 刷新批处理算子
        refreshBatchOperator(id, request);
        // 更新来源表配置
        dataSourceConfigMapper.deleteByDataModelId(id);
        updateSourceConfig(id, request.parseSourceModelIds());
        // 刷新字段
        refreshFields(id, request);
    }

    private void updateSourceConfig(Integer dataModelId, Set<Integer> sourceModelIds) {
        dataSourceConfigMapper.deleteByDataModelId(dataModelId);
        createDataSources(dataModelId, sourceModelIds);
    }

    private void refreshFields(Integer dataModelId, ThemeArrangeRequest request) {
        // 解析编排字段信息 刷新
        List<DataModelField> modelFields = parseFields(dataModelId, request);
        // 校验已建表字段是否被取消勾选
        Map<String, DataModelField> modelFieldMap = modelFields.stream()
            .collect(Collectors.toMap(DataModelField::getEnName, Function.identity()));
        List<DataModelField> createTableFields = dataModelFieldMapper.selectCreateTableFields(dataModelId);
        List<String> cancelCreateTableFieldNames = new ArrayList<>();
        for (DataModelField createTableField : createTableFields) {
            if (!modelFieldMap.containsKey(createTableField.getEnName())) {
                cancelCreateTableFieldNames.add(createTableField.getEnName());
            }
        }
        if (ObjectUtils.isNotEmpty(cancelCreateTableFieldNames)) {
            throw new BizException("已建表字段【" + cancelCreateTableFieldNames + "】不能被取消勾选，请重新编排");
        }
        // 追加字段
        List<DataModelField> dbFields = dataModelFieldMapper.selectByDataModelId(dataModelId);
        Map<String, DataModelField> dbFieldMap = dbFields.stream()
            .collect(Collectors.toMap(DataModelField::getEnName, Function.identity(), (a, b) -> a));
        List<DataModelField> needAppendFields = modelFields.stream()
            .filter(field -> !dbFieldMap.containsKey(field.getEnName()))
            .toList();
        if (ObjectUtils.isNotEmpty(needAppendFields)) {
            dataModelFieldMapper.insert(needAppendFields);
        }
        // 删除字段
        for (DataModelField field : modelFields) {
            dbFieldMap.remove(field.getEnName());
        }
        Set<Integer> deleteFieldIds = dbFieldMap.values().stream().map(AuditBaseEntity::getId)
            .collect(Collectors.toSet());
        if (ObjectUtils.isNotEmpty(deleteFieldIds)) {
            dataModelFieldMapper.deleteByIds(deleteFieldIds);
        }
    }
}
