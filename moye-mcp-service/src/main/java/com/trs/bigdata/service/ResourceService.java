package com.trs.bigdata.service;

import com.trs.bigdata.constants.McpConstants;
import com.trs.bigdata.exception.MoyeMcpServiceException;
import com.trs.bigdata.feign.StorageEngineFeign;
import com.trs.bigdata.pojo.ConditionSearchParams;
import com.trs.bigdata.pojo.FieldInfo;
import com.trs.bigdata.pojo.ResourceResult;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.SearchDataModelInfo;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.data.storage.dao.DataStorageMapper;
import com.trs.moye.base.mcp.SearchableField;
import com.trs.moye.base.mcp.TableInfo;
import io.modelcontextprotocol.server.McpServerFeatures.AsyncResourceSpecification;
import io.modelcontextprotocol.server.McpServerFeatures.SyncResourceSpecification;
import io.modelcontextprotocol.spec.McpSchema;
import io.modelcontextprotocol.spec.McpSchema.ReadResourceResult;
import io.modelcontextprotocol.spec.McpSchema.TextResourceContents;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025/4/24 11:31
 */
@Service
@Slf4j
public class ResourceService {

    @Resource
    private DataModelMapper dataModelMapper;

    @Resource
    private DataStorageMapper dataStorageMapper;

    @Resource
    private StorageEngineFeign storageEngineFeign;


    /**
     * 分页预览数据建模的数据
     *
     * @param dataModelId 数据模型id
     * @param pageNum     页码
     * @param pageSize    分页大小
     * @return 数据
     */
    public ResourceResult getDataModelDataPageable(Integer dataModelId, Integer pageNum, Integer pageSize) {
        try {
            ResourceResult result = new ResourceResult();
            DataModel dataModel = dataModelMapper.selectById(dataModelId);
            result.setInfo(new TableInfo(dataModel));
            List<DataStorage> dataStorages = dataStorageMapper.selectByDataModelIdWithConnection(dataModel.getId());
            if (dataStorages.isEmpty()) {
                result.setRows(PageResponse.of(new ArrayList<>(), pageNum, 0, pageSize));
                return result;
            }
            DataStorage dataStorage = dataStorages.get(0);
            result.getInfo().setConnectionType(dataStorage.getConnection().getConnectionType());
            result.setColumns(dataModel.getFields().stream().map(FieldInfo::new).toList());
            PageResponse<Map<String, Object>> rows = storageEngineFeign.conditionQuery(dataStorage.getConnectionId(),
                dataStorage.getEnName(), new ConditionSearchParams(pageNum, pageSize));
            result.setRows(rows);
            return result;
        } catch (Exception e) {
            log.error("获取数据模型数据失败，dataModelId: {}", dataModelId, e);
            throw new MoyeMcpServiceException("获取数据模型数据失败：" + e.getMessage(), e);
        }
    }

    /**
     * 获取大模型可用的检索数据模型
     *
     * @return 检索数据模型
     */
    public List<TableInfo> getSearchableDataModel() {
        List<SearchDataModelInfo> tables = dataModelMapper.selectSearchableTables();
        return tables.stream().map(TableInfo::new).collect(Collectors.toList());
    }

    /**
     * 创建资源
     *
     * @param tableInfo 表信息
     * @return {@link SyncResourceSpecification}
     */
    public SyncResourceSpecification createSyncResourceSpecification(TableInfo tableInfo) {
        String resourcePath = McpConstants.MCP_RESOURCE_PATH_PREFIX + tableInfo.getId();
        String desc = tableInfo.getDescription()
            + "\n包含字段信息如下："
            + tableInfo.getFields()
            .stream().map(SearchableField::getZhName).collect(Collectors.joining(","));
        return new SyncResourceSpecification(
            new McpSchema.Resource(
                resourcePath,
                tableInfo.getZhName(),
                desc,
                "application/json", null),
            (exchange, request) -> {
                String content = JsonUtils.toJsonString(getDataModelDataPageable(tableInfo.getId(), 1, 10));
                return new ReadResourceResult(List.of(
                    new TextResourceContents(resourcePath, "application/json", content)
                ));
            });
    }

    /**
     * resources
     *
     * @return {@link AsyncResourceSpecification}
     */
    public List<SyncResourceSpecification> getSyncResourceSpecifications() {
        List<SyncResourceSpecification> resourceSpecifications = new ArrayList<>();

        List<TableInfo> tableInfos = getSearchableDataModel();
        for (TableInfo tableInfo : tableInfos) {
            resourceSpecifications.add(createSyncResourceSpecification(tableInfo));
        }
        return resourceSpecifications;
    }
}
