package com.trs.moye.base.data.connection.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.trs.moye.base.common.entity.AuditBaseEntity;
import com.trs.moye.base.common.typehandler.PolymorphismTypeHandler;
import com.trs.moye.base.data.connection.entity.params.ConnectionParams;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import java.util.Objects;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 数据源 实体类
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@TableName(value = "data_connection", autoResultMap = true)
public class DataConnection extends AuditBaseEntity {

    /**
     * 数据源名称
     */
    private String name;

    /**
     * 数据源类别
     */
    private ConnectionType connectionType;

    /**
     * 数据源连接信息
     */
    @TableField(typeHandler = PolymorphismTypeHandler.class)
    private ConnectionParams connectionParams;

    /**
     * 是否为数据源
     */
    private boolean isSource;

    /**
     * 连接测试状态
     */
    private boolean isTestSuccess;

    /**
     * 证书id certificateId 允许空，指定 updateStrategy = FieldStrategy.ALWAYS 证书id为空才更新成功
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer certificateId;

    /**
     * 证书内容
     */
    @TableField(exist = false)
    private KerberosCertificate kerberosCertificate;

    @TableField(exist = false)
    private DataConnectionMonitorConfig monitorConfig;

    /**
     * 重写setConnectionParams方法，用于设置连接参数，需要检查参数类型和数据源类型是否一致，否则抛出异常
     *
     * @param connectionParams 连接参数
     * <AUTHOR>
     * @since '2024/9/28' 17:45
     **/
    public void setConnectionParams(ConnectionParams connectionParams) {
        if (Objects.isNull(connectionParams)) {
            return;
        }
        if (Objects.isNull(connectionParams.getConnectionType())) {
            throw new IllegalArgumentException("connection params must have a connection type.");
        }

        if (connectionParams.getConnectionType() != this.connectionType) {
            throw new IllegalArgumentException(
                "connection params connection type must be the same as data source.");
        }

        if (this.connectionType.getConnectionParamsClazz() != connectionParams.getClass()) {
            throw new IllegalArgumentException(
                "connection params class must be the class: " + this.connectionType.getConnectionParamsClazz().getName()
                    + ".");
        }
        this.connectionParams = connectionParams;
    }

    /**
     * 是否需要默认增量值
     *
     * @return 是否需要默认增量值
     */
    public boolean isNeedDefaultIncrement() {
        return ConnectionType.HTTP == this.connectionType
            || ConnectionType.FTP == this.getConnectionType()
            || ConnectionType.SFTP == this.getConnectionType();
//            || ConnectionType.KAFKA == this.getConnectionType()
//            || ConnectionType.ROCKETMQ == this.getConnectionType();
    }

    /**
     * 是否为文件数据源
     *
     * @return 是否为文件数据源
     */
    public boolean isFileConnection() {
        return connectionType.isFileType();
    }

    /**
     * 是否为数据库数据源
     *
     * @return 是否为数据库数据源
     */
    public boolean isDBConnection() {
        return connectionType.isDBType();
    }

    /**
     * 是否为MQ场景
     *
     * @return 是否为MQ场景
     */
    public boolean isMqConnection() {
        return connectionType.isMqType();
    }

    /**
     * 是否为HTTP数据源
     *
     * @return 数据源
     */
    public boolean isHttpConnection() {
        return connectionType.isHttpType();
    }

    /**
     * 获取路径
     *
     * @return 路径
     */
    @JsonIgnore
    public String getCatalogPath() {
        return connectionParams.getCatalogPath();
    }
}
