package com.trs.moye.base.data.model.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataModelConnDetail;
import com.trs.moye.base.data.model.entity.SearchDataModelInfo;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;

/**
 * 数据源mapper
 *
 * <AUTHOR>
 * @since 2024/9/12 16:30
 */
@Mapper
public interface DataModelMapper extends BaseMapper<DataModel> {

    /**
     * 集合查询：通过dataSourceIdCollection查询
     *
     * @param dataSourceIdCollection dataSourceId集合
     * @return 建模列表
     */
    List<DataModel> listByDataSourceIdCollection(
        @Param("dataSourceIdCollection") Collection<Integer> dataSourceIdCollection);


    /**
     * 通过id查询
     *
     * @param id id
     * @return DataModel
     */
    DataModel selectById(@Param("id") Integer id);

    /**
     * 通过id查询
     *
     * @param ids id
     * @return 携带dataStorage信息的DataModel
     */
    List<DataModel> selectWithDataStorageByIds(@Param("ids") List<Integer> ids);

    /**
     * 查询标准字段依赖项
     *
     * @param standardFieldEnName 标准字段英文名
     * @param page                页信息
     * @return 页数据
     */
    Page<DataModel> standardFieldDependList(@Param("standardFieldEnName") String standardFieldEnName,
        Page<DataModel> page);

    /**
     * 通过标准字段英文名查询元数据
     *
     * @param standardFieldEnName 标准字段英文名
     * @return 页数据
     */
    List<DataModel> listByStandardFieldEnName(@Param("standardFieldEnName") String standardFieldEnName);

    /**
     * 通过建模ID更新业务ID
     *
     * @param businessId   业务ID
     * @param dataModelIds 建模ID
     * @return {@link Boolean}
     * <AUTHOR>
     * @since 2024/9/29 16:03
     */
    boolean updateBusinessCategoryIdByIds(@Param("businessId") Integer businessId,
        @Param("dataModelIds") Collection<Integer> dataModelIds);


    /**
     * 根据id查询
     *
     * @param dataModelIds 建模ID
     * @return {@link DataModel}
     * <AUTHOR>
     * @since 2024/9/29 16:03
     */
    List<DataModel> selectByIds(@Param("dataModelIds") Collection<Integer> dataModelIds);

    /**
     * 根据id查询
     *
     * @param id 建模ID
     * @return {@link DataModel}
     * <AUTHOR>
     * @since 2024/9/29 16:03
     */
    DataModel getById(@Param("id") Integer id);

    /**
     * 根据英文名查询建模
     *
     * @param enName 英文名
     * @return {@link DataModel}
     * <AUTHOR>
     * @since 2024/9/29 16:03
     */
    DataModel getByEnName(@Param("enName") String enName);

    /**
     * 根据英文名和连接ID查询建模
     *
     * @param enName       英文名
     * @param connectionId 连接id
     * @return dataModel
     */
    DataModel getByEnNameAndConnectionId(@Param("enName") String enName, @Param("connectionId") Integer connectionId);

    /**
     * 查询dataModelId被其他的dataModel使用情况
     *
     * @param dataModelId 被使用的dataModel
     * @return {@link DataModel}
     * <AUTHOR>
     * @since 2024/10/9 17:53
     */
    List<DataModel> selectByDataModelSourceId(@Param("dataModelId") Integer dataModelId);

    /**
     * 通过层级ID查询建模
     *
     * @param id 层级ID
     * @return {@link DataModel}
     * <AUTHOR>
     * @since 2024/10/10 14:56
     */
    List<DataModel> selectByCategoryId(@Param("id") Integer id);

    /**
     * 通过分类名称查询建模
     *
     * @param categoryName 分类名称
     * @return 建模名称列表
     */
    List<String> selectNameByCategoryName(@Param("categoryName") String categoryName);


    /**
     * 更新是否同步字段
     *
     * @param dataModelId 数据建模ID
     * @param aTrue       值
     * <AUTHOR>
     * @since 2024/10/10 16:48
     */
    void updateIsSyncProperty(@Param("dataModelId") Integer dataModelId, @Param("aTrue") Boolean aTrue);

    /**
     * 更新是否已治理
     *
     * @param isArranged 是否已治理
     * @param metadataId 元数据id
     */
    void updateIsArranged(@Param("metadataId") Integer metadataId, @Param("isArranged") Boolean isArranged);

    /**
     * 更新元数据标准
     *
     * @param metaDataStandardId 元数据标准ID
     * @param dataModelId        数据建模ID
     * @param isSyncProperty     是否同步字段
     * <AUTHOR>
     * @since 2025/08/02 17:16:18
     */
    void updateMetaDataStandardId(@Param("metaDataStandardId") Integer metaDataStandardId,
        @Param("dataModelId") Integer dataModelId, @Param("isSyncProperty") Boolean isSyncProperty);

    /**
     * 查询元数据标准关联的建模数量
     *
     * @param metaDataStandardId 元数据标准ID
     * @return 建模数量
     */
    Integer countByMetaDataStandardId(@Param("metadataStandardId") Integer metaDataStandardId);


    /**
     * 通过英文名是否存在
     *
     * @param enName 英文名
     * @return {@link Boolean}
     * <AUTHOR>
     * @since 2024/10/14 11:00
     */
    Boolean existsByEnName(@Param("enName") String enName);

    /**
     * 通过数据建模ID列表查询到数据建模存储详情
     *
     * @param dataModelSourceIds 数据建模ID
     * @return {@link DataModelConnDetail}
     * <AUTHOR>
     * @since 2024/10/14 15:38
     */
    List<DataModelConnDetail> selectByDataModelIds(@Param("ids") List<Integer> dataModelSourceIds);

    /**
     * 查询启动的流处理任务
     *
     * @return {@link DataModel}
     * <AUTHOR>
     * @since 2024/10/10 14:56
     */
    List<DataModel> selectStartStreamProcessTask();


    /**
     * 按照分层模糊检索数据建模
     *
     * @param modelLayer 数据建模分层
     * @param keyword    模糊检索
     * @return {@link DataModel}
     */
    List<DataModel> selectByLayerAndKeyword(@Param("layer") ModelLayer modelLayer, @Param("keyword") String keyword);

    /**
     * 根据中文名模糊查询数据建模
     *
     * @param zhName 关键词
     * @return 数据建模列表
     */
    List<DataModel> selectByZhName(@Param("zhName") String zhName);

    /**
     * 查询流处理任务
     *
     * @return {@link DataModel}
     * <AUTHOR>
     * @since 2024/10/10 14:56
     */
    List<DataModel> selectStreamProcessTask();


    /**
     * 根据分层统计数据建模数量
     *
     * @param layer 分层类型
     * @return 数量
     */
    Long countByModelLayer(@Param("layer") ModelLayer layer);

    /**
     * 按照 建表状态 统计数据建模的数量
     *
     * @param layer 数据建模分层
     * @return 数量
     */
    Long selectCountCreateTableStatus(@Param("layer") ModelLayer layer);

    /**
     * 根绝层级查询数据建模列表
     *
     * @param layer 数据建模分层
     * @return 建模列表
     */
    List<DataModel> selectByLayer(@Param("layer") ModelLayer layer);

    /**
     * 根据层级ID查询建模
     *
     * @param id 层级ID
     * @return {@link ModelLayer}
     */
    ModelLayer selectLayerById(@Param("id") Integer id);

    /**
     * 查询启动的数据建模：所有启动的流处理数据建模
     *
     * @return 数据建模id列表
     * <AUTHOR>
     * @since 2024/9/29 16:03
     */
    Set<Integer> getStartedStreamProcessDataModelIds();

    /**
     * 判断数据建模列表是否全部都是停止状态
     *
     * @param idCollection 数据建模id集合
     * @return 是否全部停止
     */
    int getStartedDataModelCount(@Param("idCollection") Collection<Integer> idCollection);

    /**
     * 判断数据建模是否启动状态
     *
     * @param id 数据建模id
     * @return 是否启动状态
     */
    @Options(flushCache = Options.FlushCachePolicy.TRUE)
    boolean isStartStatus(@Param("id") Integer id);

    /**
     * 判断数据建模是否停止状态
     *
     * @param id 数据建模id
     * @return 是否停止状态
     */
    @Options(flushCache = Options.FlushCachePolicy.TRUE)
    boolean isStopStatus(@Param("id") Integer id);

    /**
     * 根据数据建模ID列表查询数据建模
     *
     * @param dataModelIds 数据建模ID
     * @return 数据建模列表
     */
    List<DataModel> getByIds(@Param("dataModelIds") List<Integer> dataModelIds);

    /**
     * 查询可搜索的表: 层级为主题库、描述不为空，且存储已建表的表
     *
     * @return 可搜索的表列表
     */
    List<SearchDataModelInfo> selectSearchableTables();

    /**
     * 查询治理情况的数量
     *
     * @return 治理情况的数量
     */
    Map<String, BigDecimal> selectArrangedCount();

    /**
     * 查询建模状态的数量
     *
     * @return 建模状态的数量
     */
    Map<String, BigDecimal> selectCreateTableStatusCount();

    /**
     * 各个业务分类的数量
     *
     * @return 业务分类数量
     */
    List<Map<String, Long>> selectBusinessCategoryCount();

    /**
     * 根据业务分类查询建模
     *
     * @param businessCategoryId 业务分类id
     * @return ID列表
     */
    List<Integer> selectDwdByCategoryId(@Param("businessCategoryId") Integer businessCategoryId);

    /**
     * 查询带有存储的建模
     *
     * @param dataModelIds 建模id
     * @return 建模
     */
    List<DataModel> selectByIdsWithStorage(@Param("dataModelIds") List<Integer> dataModelIds);

    /**
     * 建表成功数量
     *
     * @param modelLayer 分层
     * @return 数量
     */
    long selectCreateTableSuccessCount(@Param("layer") ModelLayer modelLayer);

    /**
     * 根据层级和分类id查询数据建模列表
     *
     * @param layer      层
     * @param categoryId 分类id
     * @return {@link List }<{@link DataModel }>
     * <AUTHOR>
     * @since 2025/05/30 14:35:02
     */
    List<DataModel> selectByLayerAndCategoryId(@Param("layer") ModelLayer layer,
        @Param("categoryId") Integer categoryId);

    /**
     * 根据layer查询数据建模ID列表
     *
     * @param modelLayer layer
     * @return id
     */
    List<Integer> selectIdsByLayer(@Param("layer") ModelLayer modelLayer);

    /**
     * 根据层级和分类名称查询数据建模列表
     *
     * @param layer    层
     * @param category 分类名称
     * @return {@link List }<{@link DataModel }>
     */
    List<DataModel> selectByLayerAndCategoryName(@Param("layer") ModelLayer layer,
        @Param("category") String category);

    /**
     * 主题库来源列表
     *
     * @param businessCategoryIds    业务分类id列表
     * @param layers                 分层列表
     * @param filterNotCreatedTables 是否过滤未建表的主题库
     * @return 建模列表
     */
    List<DataModel> listByThemeModelSources(@Param("businessCategoryIds") List<Integer> businessCategoryIds,
        @Param("layers") Collection<ModelLayer> layers,
        @Param("filterNotCreatedTables") Boolean filterNotCreatedTables);
}