package com.trs.moye.base.data.model.entity;

import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024-10-18 14:20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BatchProcessSparkConfig {

    /**
     * 凭证id
     */
    private Integer certificateId;

    /**
     * spark配置项列表，批处理任务使用
     */
    private List<SparkConfigItem> sparkConfigItemList;

    /**
     * 自定义可替换参数
     */
    private List<CodeParameterItem> customCodeParameters;

    /**
     * 遇到异常, 是否继续子任务
     * true: 继续执行后续子任务
     * false: 停止执行后续子任务
     */
    private Boolean continueOnException;

    private LocalDateTime beginTime;

    private LocalDateTime endTime;


    public BatchProcessSparkConfig(Integer certificateId, List<SparkConfigItem> sparkConfigItemList,
        List<CodeParameterItem> customCodeParameters, Boolean continueOnException) {
        this.certificateId = certificateId;
        this.sparkConfigItemList = sparkConfigItemList;
        this.customCodeParameters = customCodeParameters;
        this.continueOnException = continueOnException;
    }
}
