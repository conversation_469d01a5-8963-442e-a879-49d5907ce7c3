package com.trs.moye.storage.engine.pool;

import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.data.connection.entity.params.ConnectionParams;
import com.trs.moye.storage.engine.common.MoyeConnection;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2025-08-06 11:05
 */
@Slf4j
public class SimulateMoyeConnection implements MoyeConnection {

    private final ConnectionParams connectionParams;

    public SimulateMoyeConnection(ConnectionParams connectionParams) {
        this.connectionParams = connectionParams;
    }

    @Override
    public boolean testConnection() {
        return true;
    }

    @Override
    public void testConnectionWithException() throws Exception {

    }

    @Override
    public void close() throws Exception {
        log.warn("");
    }

    /**
     * 模拟业务耗时
     *
     * @param businessTime 业务耗时
     */
    public void simulateBusiness(long businessTime) {
        try {
            String identification = connectionParams.getIdentification();
            log.info("业务[{}]，耗时：{}", identification, businessTime);
            Thread.sleep(businessTime);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new BizException(e);
        }
    }
}
