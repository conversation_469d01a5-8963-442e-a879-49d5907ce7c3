package com.trs.moye.storage.engine.pool;

import com.trs.moye.storage.engine.pool.monitor.PoolBorrowMonitor;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

/**
 * <AUTHOR>
 * @since 2025-08-06 10:28
 */
class PoolTest {

    private PoolConfig poolConfig;

    private ConnectionFactory connectionFactory;

    private ConnectionPool connectionPool;

    private PoolBorrowMonitor borrowMonitor;

//    @BeforeEach
//    void setUp() {
//        poolConfig = new PoolConfig();
//        poolConfig.setIdleCheckTime(1000);
//        poolConfig.setMaxCount(3);
//        connectionFactory = new ConnectionFactory();
//        borrowMonitor = new PoolBorrowMonitor();
//        connectionPool = new ConnectionPool(connectionFactory, poolConfig, borrowMonitor);
//    }


    @ParameterizedTest
    @ValueSource(ints = {3})
    void emptyTest(int param) {
        Assertions.assertTrue(true, "空测试");
    }

//    @ParameterizedTest
//    @ValueSource(strings = {"3,2"})
//    void test(String param) {
//        String[] split = param.split(",");
//    }

}
